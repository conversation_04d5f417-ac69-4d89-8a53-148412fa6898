<?php
/**
 * Test PDF Templates for CFB Calculator
 * Test all three PDF templates with form data
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

// Security check
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

echo "<h1>CFB Calculator - PDF Templates Test</h1>";

// Check if plugin is active
if (!class_exists('CFB_Calculator')) {
    echo "<p style='color: red;'>❌ CFB Calculator plugin is not active!</p>";
    exit;
}

// Check if TCPDF is available
if (!class_exists('TCPDF')) {
    echo "<p style='color: red;'>❌ TCPDF library not found!</p>";
    echo "<p>Please install TCPDF library first.</p>";
    exit;
}

echo "<p style='color: green;'>✅ CFB Calculator plugin and TCPDF library are ready</p>";

// Create test invoice data
$test_invoice = (object) array(
    'id' => 999,
    'invoice_number' => 'TEST-' . date('Ym') . '-0001',
    'form_id' => 1,
    'form_name' => 'Test Calculator Form',
    'customer_name' => 'احمد محمدی', // Persian name for RTL testing
    'customer_email' => '<EMAIL>',
    'customer_phone' => '۰۹۱۲۳۴۵۶۷۸۹', // Persian digits
    'customer_address' => 'تهران، خیابان ولیعصر، پلاک ۱۲۳', // Persian address
    'subtotal' => 1250.75,
    'tax_amount' => 125.08,
    'total_amount' => 1375.83,
    'currency' => 'USD',
    'status' => 'draft',
    'notes' => 'This is a test invoice with Persian text: این یک فاکتور تست است',
    'created_at' => date('Y-m-d H:i:s'),
    'form_data' => json_encode(array(
        'project_name' => 'پروژه نمونه',
        'project_type' => 'Website Development',
        'duration_months' => '۶',
        'team_size' => '۴',
        'complexity' => 'High',
        'features' => array('Responsive Design', 'E-commerce', 'Multi-language'),
        'budget_range' => '$10,000 - $15,000',
        'deadline' => '۱۴۰۳/۰۶/۱۵'
    )),
    'items' => array()
);

// Test all three templates
$templates = array(
    'modern' => 'Modern Template',
    'classic' => 'Classic Template (with Form Fields)',
    'minimal' => 'Minimal Template (with Form Fields)'
);

echo "<h2>Testing PDF Templates:</h2>";

$pdf_generator = CFB_PDF_Generator::get_instance();

foreach ($templates as $template_key => $template_name) {
    echo "<h3>Testing $template_name:</h3>";
    
    // Set the template option
    update_option('cfb_pdf_template', $template_key);
    
    try {
        // Generate PDF
        $pdf_path = $pdf_generator->generate_invoice_pdf($test_invoice);
        
        if ($pdf_path) {
            $upload_dir = wp_upload_dir();
            $pdf_url = $upload_dir['baseurl'] . '/' . $pdf_path;
            
            echo "<p style='color: green;'>✅ $template_name generated successfully!</p>";
            echo "<p><a href='$pdf_url' target='_blank' style='background: #0073aa; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>📄 View $template_name PDF</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to generate $template_name</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error generating $template_name: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Test settings
echo "<h2>Current PDF Settings:</h2>";
$settings = array(
    'cfb_pdf_template' => get_option('cfb_pdf_template', 'modern'),
    'cfb_pdf_font_family' => get_option('cfb_pdf_font_family', 'helvetica'),
    'cfb_pdf_font_size' => get_option('cfb_pdf_font_size', 10),
    'cfb_pdf_color_scheme' => get_option('cfb_pdf_color_scheme', 'blue'),
    'cfb_pdf_rtl_support' => get_option('cfb_pdf_rtl_support', 0),
    'cfb_convert_to_farsi_digits' => get_option('cfb_convert_to_farsi_digits', 0),
    'cfb_invoice_title' => get_option('cfb_invoice_title', ''),
    'cfb_company_name' => get_option('cfb_company_name', ''),
    'cfb_company_logo' => get_option('cfb_company_logo', '')
);

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
foreach ($settings as $key => $value) {
    $display_value = is_array($value) ? json_encode($value) : (string)$value;
    if (empty($display_value)) $display_value = '<em>Not set</em>';
    echo "<tr><td><strong>$key</strong></td><td>$display_value</td></tr>";
}
echo "</table>";

// Test Farsi digit conversion
echo "<h2>Testing Farsi Digit Conversion:</h2>";

// Enable Farsi digits for testing
update_option('cfb_convert_to_farsi_digits', 1);

echo "<p><strong>English:</strong> 1234567890</p>";
echo "<p><strong>Farsi:</strong> ۱۲۳۴۵۶۷۸۹۰</p>";

// Test RTL support
echo "<h2>Testing RTL Support:</h2>";

// Enable RTL for testing
update_option('cfb_pdf_rtl_support', 1);

echo "<p><strong>RTL Enabled:</strong> Text should be right-aligned in PDF</p>";
echo "<p><strong>Persian Text:</strong> این متن باید از راست به چپ نمایش داده شود</p>";

// Generate RTL test PDF
try {
    update_option('cfb_pdf_template', 'classic');
    $rtl_pdf_path = $pdf_generator->generate_invoice_pdf($test_invoice);
    
    if ($rtl_pdf_path) {
        $upload_dir = wp_upload_dir();
        $rtl_pdf_url = $upload_dir['baseurl'] . '/' . $rtl_pdf_path;
        
        echo "<p style='color: green;'>✅ RTL PDF generated successfully!</p>";
        echo "<p><a href='$rtl_pdf_url' target='_blank' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>📄 View RTL PDF</a></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error generating RTL PDF: " . $e->getMessage() . "</p>";
}

// Reset settings
update_option('cfb_pdf_template', 'modern');
update_option('cfb_convert_to_farsi_digits', 0);
update_option('cfb_pdf_rtl_support', 0);

echo "<h2>Template Features Comparison:</h2>";
echo "<table border='1' cellpadding='8' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f5f5f5;'>";
echo "<th>Feature</th><th>Modern</th><th>Classic</th><th>Minimal</th>";
echo "</tr>";

$features = array(
    'Form Fields Display' => array('❌', '✅', '✅'),
    'Bordered Layout' => array('❌', '✅', '❌'),
    'Color Backgrounds' => array('✅', '✅', '❌'),
    'Compact Design' => array('❌', '❌', '✅'),
    'Professional Headers' => array('✅', '✅', '❌'),
    'Detailed Sections' => array('✅', '✅', '❌'),
    'RTL Support' => array('✅', '✅', '✅'),
    'Farsi Digits' => array('✅', '✅', '✅'),
    'Custom Logo' => array('✅', '✅', '✅'),
    'Custom Title' => array('✅', '✅', '✅')
);

foreach ($features as $feature => $support) {
    echo "<tr>";
    echo "<td><strong>$feature</strong></td>";
    foreach ($support as $status) {
        $color = $status === '✅' ? 'green' : 'red';
        echo "<td style='text-align: center; color: $color;'>$status</td>";
    }
    echo "</tr>";
}
echo "</table>";

echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li>✅ Test all three PDF templates</li>";
echo "<li>✅ Verify form fields display in Classic and Minimal templates</li>";
echo "<li>✅ Test Farsi digit conversion</li>";
echo "<li>✅ Test RTL support</li>";
echo "<li>🔄 Upload company logo and test logo placement</li>";
echo "<li>🔄 Set custom invoice title and test</li>";
echo "<li>🔄 Install Persian fonts for better typography</li>";
echo "</ul>";

echo "<p><a href='admin.php?page=cfb-calculator-settings' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>⚙️ Go to CFB Settings</a></p>";
?>
