<?php
/**
 * Test Variable Save Functionality
 * Quick test to debug variable save issues
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Variable Save Test</h1>";

// Test 1: Check if classes exist
echo "<h2>1. Class Check</h2>";
if (class_exists('CFB_Database')) {
    echo "✅ CFB_Database class exists<br>";
} else {
    echo "❌ CFB_Database class missing<br>";
    exit;
}

if (class_exists('CFB_Variables')) {
    echo "✅ CFB_Variables class exists<br>";
} else {
    echo "❌ CFB_Variables class missing<br>";
    exit;
}

// Test 2: Check database table
echo "<h2>2. Database Table Check</h2>";
global $wpdb;
$table_name = $wpdb->prefix . 'cfb_variables';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

if ($table_exists) {
    echo "✅ Variables table exists: $table_name<br>";
    
    // Show table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo "<strong>Table structure:</strong><br>";
    foreach ($columns as $column) {
        echo "- {$column->Field} ({$column->Type})<br>";
    }
} else {
    echo "❌ Variables table missing: $table_name<br>";
    echo "Creating table...<br>";
    
    try {
        CFB_Database::get_instance()->create_tables();
        echo "✅ Table creation attempted<br>";
        
        // Check again
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
        if ($table_exists) {
            echo "✅ Table now exists<br>";
        } else {
            echo "❌ Table creation failed<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error creating table: " . $e->getMessage() . "<br>";
    }
}

// Test 3: Test direct database save
echo "<h2>3. Direct Database Save Test</h2>";
try {
    $db = CFB_Database::get_instance();
    
    $test_data = array(
        'name' => 'test_var_' . time(),
        'label' => 'Test Variable',
        'value' => 10.50,
        'description' => 'Test variable for debugging',
        'category' => 'general',
        'icon' => 'dashicons-admin-settings',
        'color' => '#667eea',
        'is_active' => 1
    );
    
    echo "Test data: " . print_r($test_data, true) . "<br>";
    
    $variable_id = $db->save_variable($test_data);
    
    if ($variable_id) {
        echo "✅ Variable saved successfully! ID: $variable_id<br>";
        
        // Verify it was saved
        $saved_variable = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $variable_id));
        if ($saved_variable) {
            echo "✅ Variable verified in database:<br>";
            echo "- Name: {$saved_variable->name}<br>";
            echo "- Label: {$saved_variable->label}<br>";
            echo "- Value: {$saved_variable->value}<br>";
        }
        
        // Clean up
        $wpdb->delete($table_name, array('id' => $variable_id), array('%d'));
        echo "✅ Test variable cleaned up<br>";
    } else {
        echo "❌ Variable save failed<br>";
        echo "Last database error: " . $wpdb->last_error . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Exception during save test: " . $e->getMessage() . "<br>";
}

// Test 4: Test AJAX endpoint
echo "<h2>4. AJAX Endpoint Test</h2>";
$ajax_url = admin_url('admin-ajax.php');
echo "AJAX URL: $ajax_url<br>";

// Check if AJAX action is registered
if (has_action('wp_ajax_cfb_save_variable')) {
    echo "✅ AJAX action 'cfb_save_variable' is registered<br>";
} else {
    echo "❌ AJAX action 'cfb_save_variable' not registered<br>";
}

// Test 5: Simulate AJAX request
echo "<h2>5. AJAX Simulation Test</h2>";
try {
    // Set up POST data
    $_POST = array(
        'action' => 'cfb_save_variable',
        'nonce' => wp_create_nonce('cfb_admin_nonce'),
        'name' => 'ajax_test_var',
        'label' => 'AJAX Test Variable',
        'value' => 25.75,
        'description' => 'Test via AJAX simulation',
        'category' => 'general',
        'icon' => 'dashicons-money-alt',
        'color' => '#28a745',
        'is_active' => 1
    );
    
    echo "Simulating AJAX request with data:<br>";
    echo print_r($_POST, true) . "<br>";
    
    // Capture output
    ob_start();
    
    // Call the AJAX handler directly
    $variables_instance = CFB_Variables::get_instance();
    $variables_instance->ajax_save_variable();
    
    $output = ob_get_clean();
    
    echo "AJAX Response: $output<br>";
    
    // Try to decode JSON response
    $response = json_decode($output, true);
    if ($response) {
        if ($response['success']) {
            echo "✅ AJAX save successful!<br>";
            echo "Message: " . $response['data']['message'] . "<br>";
            if (isset($response['data']['variable_id'])) {
                echo "Variable ID: " . $response['data']['variable_id'] . "<br>";
                
                // Clean up
                $wpdb->delete($table_name, array('id' => $response['data']['variable_id']), array('%d'));
                echo "✅ Test variable cleaned up<br>";
            }
        } else {
            echo "❌ AJAX save failed: " . $response['data'] . "<br>";
        }
    } else {
        echo "❌ Invalid JSON response<br>";
    }
    
} catch (Exception $e) {
    echo "❌ AJAX simulation error: " . $e->getMessage() . "<br>";
}

// Test 6: Check current variables
echo "<h2>6. Current Variables</h2>";
$variables = $wpdb->get_results("SELECT * FROM $table_name ORDER BY id DESC LIMIT 5");
if ($variables) {
    echo "Current variables in database:<br>";
    foreach ($variables as $var) {
        echo "- ID: {$var->id}, Name: {$var->name}, Label: {$var->label}, Value: {$var->value}<br>";
    }
} else {
    echo "No variables found in database<br>";
}

// Test 7: Check WordPress user permissions
echo "<h2>7. User Permissions</h2>";
if (current_user_can('manage_options')) {
    echo "✅ Current user can manage options<br>";
} else {
    echo "❌ Current user cannot manage options<br>";
}

echo "Current user ID: " . get_current_user_id() . "<br>";
echo "Current user roles: " . implode(', ', wp_get_current_user()->roles) . "<br>";

echo "<h2>✅ Variable Save Test Complete</h2>";
echo "<p>Check the results above to identify any issues with variable saving.</p>";

// Show recent error log entries
echo "<h2>8. Recent Error Log Entries</h2>";
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    $recent_cfb_logs = array();
    
    foreach (array_reverse($log_lines) as $line) {
        if (strpos($line, 'CFB') !== false && count($recent_cfb_logs) < 10) {
            $recent_cfb_logs[] = $line;
        }
    }
    
    if ($recent_cfb_logs) {
        echo "Recent CFB log entries:<br>";
        foreach ($recent_cfb_logs as $log) {
            echo "<code>" . esc_html($log) . "</code><br>";
        }
    } else {
        echo "No recent CFB log entries found<br>";
    }
} else {
    echo "Debug log file not found at: $log_file<br>";
}
?>
