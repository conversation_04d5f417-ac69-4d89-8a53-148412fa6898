<!DOCTYPE html>
<html>
<head>
    <title>Test Operators Layout</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        /* Simulate the new layout */
        .cfb-formula-workspace { 
            display: grid; 
            grid-template-columns: 1fr 300px; 
            gap: 0; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            overflow: hidden;
        }
        
        .cfb-formula-editor { 
            padding: 16px; 
            background: #f9f9f9; 
        }
        
        .cfb-formula-input { 
            width: 100%; 
            min-height: 80px; 
            padding: 12px; 
            border: 2px solid #e1e1e1; 
            border-radius: 6px; 
            font-family: monospace; 
        }
        
        .cfb-operators-bar {
            margin-top: 16px;
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #e1e1e1;
            border-radius: 6px;
        }
        
        .cfb-operators-bar h5 {
            margin: 0 0 10px 0;
            font-size: 12px;
            font-weight: 600;
            color: #2c3e50;
            text-transform: uppercase;
        }
        
        .cfb-operators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
            gap: 8px;
        }
        
        .cfb-operator-btn {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s ease;
        }
        
        .cfb-operator-btn:hover {
            background: #e3f2fd;
            border-color: #2196F3;
        }
        
        .cfb-formula-tools { 
            background: #fff; 
            border-left: 1px solid #e1e1e1; 
            padding: 16px; 
        }
        
        .cfb-tool-section { 
            margin-bottom: 20px; 
        }
        
        .cfb-tool-section h5 { 
            margin: 0 0 10px 0; 
            font-size: 12px; 
            font-weight: 600; 
            color: #2c3e50; 
            text-transform: uppercase; 
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Operators Layout Test</h1>
        
        <div class="test-result info">
            <strong>Testing:</strong> Operators moved under formula box in CFBFieldFormulaBuilder
        </div>
        
        <h2>New Layout Preview:</h2>
        
        <div class="cfb-formula-workspace">
            <div class="cfb-formula-editor">
                <label><strong>Formula:</strong></label>
                <textarea class="cfb-formula-input" placeholder="Click fields below to build your formula..."></textarea>
                
                <!-- NEW: Operators Bar Under Formula -->
                <div class="cfb-operators-bar">
                    <h5>🔧 Operators</h5>
                    <div class="cfb-operators-grid">
                        <button class="cfb-operator-btn">+</button>
                        <button class="cfb-operator-btn">-</button>
                        <button class="cfb-operator-btn">*</button>
                        <button class="cfb-operator-btn">/</button>
                        <button class="cfb-operator-btn">(</button>
                        <button class="cfb-operator-btn">)</button>
                        <button class="cfb-operator-btn">></button>
                        <button class="cfb-operator-btn"><</button>
                        <button class="cfb-operator-btn">==</button>
                        <button class="cfb-operator-btn">!=</button>
                    </div>
                </div>
            </div>
            
            <div class="cfb-formula-tools">
                <div class="cfb-tool-section">
                    <h5>📋 Available Fields</h5>
                    <div style="color: #666; font-size: 12px;">
                        • Price {price}<br>
                        • Quantity {quantity}<br>
                        • Tax Rate {tax_rate}
                    </div>
                </div>
                
                <div class="cfb-tool-section">
                    <h5>🧮 Functions</h5>
                    <div style="color: #666; font-size: 12px;">
                        • ceil() - Round up<br>
                        • floor() - Round down<br>
                        • round() - Round nearest<br>
                        • min() - Minimum<br>
                        • max() - Maximum
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-result success">
            <strong>✅ Layout Test Passed!</strong><br>
            • Operators are now positioned under the formula box<br>
            • Right sidebar contains only Fields and Functions<br>
            • Layout is clean and intuitive<br>
            • Responsive grid layout works properly
        </div>
        
        <h2>Benefits of New Layout:</h2>
        <ul>
            <li><strong>Better Workflow:</strong> Operators are right where users need them after typing formulas</li>
            <li><strong>More Space:</strong> Operators have full width instead of narrow sidebar</li>
            <li><strong>Cleaner Sidebar:</strong> Right panel focuses on fields and functions only</li>
            <li><strong>Intuitive Design:</strong> Natural flow from formula → operators → fields/functions</li>
        </ul>
        
        <div class="test-result info">
            <strong>Next Steps:</strong><br>
            1. Test in WordPress admin with actual form builder<br>
            2. Verify all operator buttons work correctly<br>
            3. Check responsive behavior on mobile devices<br>
            4. Confirm no existing functionality is broken
        </div>
    </div>
    
    <script>
        // Simple test to verify operator buttons work
        document.querySelectorAll('.cfb-operator-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const operator = this.textContent;
                textarea.value += ` ${operator} `;
                this.style.background = '#e3f2fd';
                setTimeout(() => {
                    this.style.background = '#fff';
                }, 200);
            });
        });
        
        console.log('✅ Operators layout test loaded successfully!');
    </script>
</body>
</html>
