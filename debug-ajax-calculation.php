<?php
/**
 * Debug AJAX Calculation
 * Simulate the exact AJAX call that frontend makes
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>Debug AJAX Calculation</h1>";
echo "<p>Simulating the exact AJAX call from frontend</p>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simulate the AJAX request
$_POST = array(
    'action' => 'cfb_calculate_price',
    'nonce' => wp_create_nonce('cfb_calculator_nonce'),
    'form_id' => 1, // Use an existing form ID
    'form_data' => array(
        'dropdown_4' => '6000'
    )
);

echo "<h2>1. Simulated POST Data:</h2>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

// Check if form exists
global $wpdb;
$forms_table = $wpdb->prefix . 'cfb_forms';
$forms = $wpdb->get_results("SELECT id, name FROM $forms_table LIMIT 5");

echo "<h2>2. Available Forms:</h2>";
if (empty($forms)) {
    echo "❌ No forms found in database!<br>";
    
    // Create a test form
    echo "<h3>Creating test form...</h3>";
    $test_form_config = array(
        'fields' => array(
            array(
                'type' => 'dropdown',
                'name' => 'dropdown_4',
                'label' => 'Dropdown 4',
                'options' => array(
                    array('label' => '1000', 'value' => '1000'),
                    array('label' => '6000', 'value' => '6000'),
                    array('label' => '10000', 'value' => '10000')
                )
            ),
            array(
                'type' => 'calculation',
                'name' => 'calculation_result',
                'label' => 'Calculation Result',
                'formula' => '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))',
                'display_type' => 'currency'
            )
        )
    );
    
    $result = $wpdb->insert(
        $forms_table,
        array(
            'name' => 'Test Formula Form',
            'description' => 'Test form for formula debugging',
            'form_data' => wp_json_encode($test_form_config),
            'status' => 'active',
            'created_at' => current_time('mysql')
        ),
        array('%s', '%s', '%s', '%s', '%s')
    );
    
    if ($result) {
        $test_form_id = $wpdb->insert_id;
        echo "✅ Created test form with ID: $test_form_id<br>";
        $_POST['form_id'] = $test_form_id;
    } else {
        echo "❌ Failed to create test form<br>";
        exit;
    }
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th></tr>";
    foreach ($forms as $form) {
        echo "<tr><td>{$form->id}</td><td>{$form->name}</td></tr>";
    }
    echo "</table>";
    
    // Use the first form
    $_POST['form_id'] = $forms[0]->id;
    echo "<p>Using form ID: {$forms[0]->id}</p>";
}

echo "<h2>3. Testing Formula Engine Directly:</h2>";

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "✅ Formula engine created<br>";
    
    // Test the formula directly
    $test_formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))';
    echo "<p><strong>Testing formula:</strong> <code>$test_formula</code></p>";
    
    $result = $formula_engine->evaluate_formula($test_formula);
    echo "<p><strong>Direct result:</strong> $result</p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<h2>4. Testing AJAX Handler:</h2>";

try {
    // Capture output
    ob_start();
    
    // Call the AJAX handler directly
    $formula_engine = CFB_Formula_Engine::get_instance();
    $formula_engine->calculate_price();
    
    $ajax_output = ob_get_clean();
    
    echo "<h3>AJAX Response:</h3>";
    echo "<pre>$ajax_output</pre>";
    
    // Try to decode JSON response
    $response_data = json_decode($ajax_output, true);
    if ($response_data) {
        echo "<h3>Parsed Response:</h3>";
        echo "<pre>" . print_r($response_data, true) . "</pre>";
        
        if (isset($response_data['success']) && $response_data['success']) {
            echo "<p style='color: green;'>✅ AJAX call successful!</p>";
            
            if (isset($response_data['data']['calculations'])) {
                echo "<h4>Calculations:</h4>";
                foreach ($response_data['data']['calculations'] as $calc) {
                    echo "<p>• {$calc['label']}: {$calc['value']} (formatted: {$calc['formatted']})</p>";
                }
            }
            
            if (isset($response_data['data']['total'])) {
                echo "<h4>Total: {$response_data['data']['total']}</h4>";
            }
        } else {
            echo "<p style='color: red;'>❌ AJAX call failed</p>";
            if (isset($response_data['data'])) {
                echo "<p>Error: {$response_data['data']}</p>";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Could not parse JSON response</p>";
    }
    
} catch (Exception $e) {
    echo "❌ AJAX Error: " . $e->getMessage() . "<br>";
}

echo "<h2>5. Check Error Logs:</h2>";
echo "<p>Check your WordPress error logs for detailed debugging information.</p>";

// Clean up test form if we created one
if (isset($test_form_id)) {
    $wpdb->delete($forms_table, array('id' => $test_form_id), array('%d'));
    echo "<p>✅ Cleaned up test form</p>";
}
?>
