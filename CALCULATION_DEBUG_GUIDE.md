# 🔧 CFB Calculator - Calculation Debug Guide

## 🚨 **Current Issue**
Frontend calculation shows: "Network error occurred: Internal Server Error"

## 🔍 **Debug Steps Added**

### 1. **Enhanced Error Logging**
- Added comprehensive error logging to formula engine
- Logs all incoming AJAX requests
- Tracks each step of calculation process
- Captures both Exceptions and Fatal Errors

### 2. **Debug Test File Created**
- Created `debug-calculation.php` in root directory
- Tests class existence, database tables, forms
- Simulates calculation process
- Provides detailed error information

### 3. **AJAX Handler Fixes**
- Removed duplicate nonce verification
- Added better error handling
- Improved logging for debugging

## 🔧 **How to Debug**

### **Step 1: Check Error Logs**
1. Enable WordPress debug logging in `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

2. Check error log file (usually in `/wp-content/debug.log`)
3. Look for entries starting with "CFB"

### **Step 2: Run Debug Test**
1. Upload `debug-calculation.php` to your WordPress root
2. Access: `yoursite.com/debug-calculation.php`
3. Review all test results
4. Check for any errors or missing components

### **Step 3: Test Simple Form**
Create a simple test form with:
- 1 Number field (name: "quantity")
- 1 Calculation field (formula: "{quantity} * 10")
- Test calculation on frontend

### **Step 4: Check Browser Console**
1. Open browser developer tools
2. Go to Console tab
3. Try calculation and check for JavaScript errors
4. Check Network tab for AJAX request details

## 🎯 **Common Issues & Solutions**

### **Issue 1: Missing Form Fields**
**Symptom**: "Form has no fields configured"
**Solution**: Ensure form has been saved with fields

### **Issue 2: Invalid Form Configuration**
**Symptom**: "Invalid form configuration"
**Solution**: Check if form_data is valid JSON

### **Issue 3: Nonce Verification Failed**
**Symptom**: "Security check failed"
**Solution**: Check if nonce is being passed correctly

### **Issue 4: Formula Evaluation Error**
**Symptom**: Error in formula processing
**Solution**: Check formula syntax and field references

## 🔧 **Quick Fixes to Try**

### **Fix 1: Disable Nonce Temporarily**
In `includes/class-cfb-formula-engine.php`, comment out nonce check:
```php
// if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'cfb_calculator_nonce')) {
//     error_log('CFB Nonce verification failed...');
//     wp_send_json_error(__('Security check failed', 'cfb-calculator'));
//     return;
// }
```

### **Fix 2: Simple Calculation Test**
Replace calculation logic with simple test:
```php
// Simple test - just return success
wp_send_json_success(array(
    'calculations' => array(),
    'total' => 100,
    'formatted_total' => '$100.00',
    'variables' => array()
));
```

### **Fix 3: Check Database Connection**
Add database test:
```php
global $wpdb;
$test = $wpdb->get_var("SELECT 1");
if ($test !== '1') {
    wp_send_json_error('Database connection failed');
}
```

## 📋 **Debug Checklist**

### **Backend Checks:**
- [ ] WordPress debug logging enabled
- [ ] CFB classes loaded correctly
- [ ] Database tables exist
- [ ] Forms exist in database
- [ ] Form data is valid JSON
- [ ] AJAX handlers registered

### **Frontend Checks:**
- [ ] JavaScript console shows no errors
- [ ] AJAX request reaches server
- [ ] Correct nonce being sent
- [ ] Form data being collected properly
- [ ] Response handling working

### **Form Checks:**
- [ ] Form has fields configured
- [ ] Field names are unique
- [ ] Calculation fields have formulas
- [ ] No circular references in formulas

## 🚀 **Next Steps**

1. **Run debug test** and check results
2. **Check error logs** for specific errors
3. **Test with simple form** first
4. **Gradually add complexity** once basic calculation works

## 📞 **Getting Help**

If issues persist:
1. Share error log entries (starting with "CFB")
2. Share debug test results
3. Share form configuration JSON
4. Share browser console errors

The enhanced logging will help identify exactly where the calculation is failing! 🔍
