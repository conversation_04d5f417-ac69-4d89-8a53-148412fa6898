# CFB Calculator Plugin - Complete Setup Guide

## 🚀 **Quick Start Guide**

### **Installation Steps:**

1. **Upload Plugin:**
   ```
   Upload the entire 'cfb-calculator' folder to /wp-content/plugins/
   ```

2. **Activate Plugin:**
   ```
   Go to WordPress Admin > Plugins > Activate "CFB Calculator"
   ```

3. **Access Plugin:**
   ```
   WordPress Admin > CFB Calculator
   ```

## 🔧 **Fixed Issues & New Features**

### ✅ **Issues Resolved:**

1. **✅ Form Loading Fixed** - Edit forms now properly load saved fields and settings
2. **✅ Conditional Logic Working** - Complete conditional logic builder with UI
3. **✅ Professional Formula Builder** - Syntax highlighting, validation, autocomplete
4. **✅ Database Integration** - All data properly saved and retrieved
5. **✅ Calculation Engine** - Real-time calculations with complex formulas
6. **✅ Beautiful UI** - Professional admin interface with modern design

### 🎯 **New Professional Features:**

#### **1. Advanced Formula Builder**
- **Syntax Highlighting** - Color-coded formulas for easy reading
- **Real-time Validation** - Instant error checking
- **Smart Autocomplete** - Field and function suggestions
- **Visual Toolbar** - Click to insert fields, functions, operators
- **Help System** - Built-in documentation and examples

#### **2. Complete Conditional Logic**
- **Visual Builder** - Drag & drop condition creation
- **Multiple Operators** - equals, greater than, contains, etc.
- **AND/OR Logic** - Complex condition combinations
- **Real-time Preview** - See conditions as you build them

#### **3. Professional Admin Interface**
- **Modern Design** - Clean, intuitive interface
- **Responsive Layout** - Works on all devices
- **Drag & Drop** - Easy field arrangement
- **Live Preview** - See changes instantly

## 📝 **Step-by-Step Tutorial**

### **Step 1: Create Your First Form**

1. Go to **CFB Calculator > Add New Form**
2. Enter form name: "Website Quote Calculator"
3. Enter description: "Get an instant quote for your website project"

### **Step 2: Add Basic Fields**

1. **Drag "Dropdown"** from Field Types to canvas
2. **Edit the field:**
   - Label: "Website Type"
   - Name: "website_type"
   - Options:
     - Basic Website | basic | $500
     - Business Website | business | $1200
     - E-commerce | ecommerce | $2500

3. **Drag "Number Field"** to canvas
4. **Edit the field:**
   - Label: "Number of Pages"
   - Name: "page_count"
   - Min: 1, Max: 50, Default: 5

5. **Drag "Checkboxes"** to canvas
6. **Edit the field:**
   - Label: "Additional Features"
   - Name: "features"
   - Options:
     - SEO Optimization | seo | $300
     - Contact Form | contact | $150
     - Blog Setup | blog | $200

### **Step 3: Set Up Conditional Logic**

1. **Add another Checkbox field:**
   - Label: "Premium Features"
   - Name: "premium_features"

2. **Enable Conditional Logic:**
   - Check "Enable conditional logic"
   - Logic Type: "All conditions"
   - Condition: website_type equals "business" OR "ecommerce"

3. **Add Premium Options:**
   - Advanced Analytics | analytics | $400
   - Custom Design | design | $800
   - Priority Support | support | $200

### **Step 4: Create Calculation Formula**

1. **Enable Subtotals** in Form Settings

2. **Add Subtotal 1:**
   - Label: "Base Cost"
   - Formula: `{website_type} + ({page_count} * 50)`

3. **Add Subtotal 2:**
   - Label: "Features Cost"
   - Formula: `{features} + {premium_features}`

4. **Set Total Formula:**
   ```
   if({page_count} > 20, 
       (subtotal_1 + subtotal_2) * 0.9, 
       subtotal_1 + subtotal_2
   )
   ```

### **Step 5: Test Your Form**

1. **Save the form**
2. **Copy the shortcode:** `[cfb_calculator id="1"]`
3. **Add to any page/post**
4. **Test all scenarios:**
   - Different website types
   - Various page counts
   - Feature combinations
   - Conditional logic triggers

## 🧮 **Formula Examples**

### **Basic Calculations**
```javascript
// Simple multiplication
{quantity} * {price}

// Percentage calculation
{base_price} * 1.15

// Area calculation
{length} * {width} * {height}
```

### **Conditional Formulas**
```javascript
// Bulk discount
if({quantity} > 10, {price} * 0.9, {price})

// Tiered pricing
if({quantity} > 100, {price} * 0.8, 
   if({quantity} > 50, {price} * 0.9, {price}))

// Minimum charge
max({calculated_price}, 100)
```

### **Advanced Functions**
```javascript
// Round up to nearest 10
ceil({total} / 10) * 10

// Complex calculation with multiple conditions
if({service_type} == "premium",
   ceil(({base_cost} + {features}) * 1.2),
   floor(({base_cost} + {features}) * 1.1)
)
```

## 🎨 **UI Customization**

### **Custom CSS Examples**

Add to **Settings > Advanced > Custom CSS:**

```css
/* Modern gradient theme */
.cfb-calculator-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
}

/* Custom button styling */
.cfb-calculate-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 25px;
    padding: 15px 30px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Animated total display */
.cfb-total-value {
    font-size: 2.5em;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* RTL specific styles */
.cfb-calculator-wrapper[dir="rtl"] {
    font-family: 'Vazir', 'Tahoma', sans-serif;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-total-value {
    direction: rtl;
    text-align: right;
}
```

## 🌍 **Multi-Language Setup**

### **For Farsi/Persian:**

1. **Settings > Language:**
   - Enable RTL Support: ✅
   - Default Language: Farsi (Persian)
   - Currency Symbol: ﷼
   - Currency Position: Right

2. **Custom CSS for Farsi:**
```css
.cfb-calculator-wrapper[dir="rtl"] {
    font-family: 'Vazir', 'IRANSans', 'Tahoma', sans-serif;
    direction: rtl;
    text-align: right;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-field-label {
    text-align: right;
}

.cfb-calculator-wrapper[dir="rtl"] .cfb-total-value {
    direction: rtl;
}
```

## 🔧 **Troubleshooting**

### **Common Issues & Solutions:**

#### **1. Calculations Not Working**
- ✅ Check field names in formulas match exactly
- ✅ Verify AJAX is working (check browser console)
- ✅ Ensure all required fields have values

#### **2. Conditional Logic Not Showing/Hiding**
- ✅ Check condition field names are correct
- ✅ Verify operator selection (equals vs contains)
- ✅ Test with simple conditions first

#### **3. Form Not Saving**
- ✅ Check user permissions (manage_options capability)
- ✅ Verify nonce security tokens
- ✅ Check for JavaScript errors in console

#### **4. Styling Issues**
- ✅ Clear cache after CSS changes
- ✅ Check for theme conflicts
- ✅ Use browser developer tools to debug

## 📊 **Performance Tips**

1. **Optimize Formulas:**
   - Use simple operators when possible
   - Avoid deeply nested conditions
   - Cache complex calculations

2. **Reduce Field Count:**
   - Combine related options
   - Use conditional logic to show/hide
   - Group similar fields

3. **Enable Caching:**
   - Settings > Advanced > Enable Caching
   - Use WordPress caching plugins
   - Optimize database queries

## 🎯 **Best Practices**

### **Form Design:**
1. **Logical Flow** - Order fields from general to specific
2. **Clear Labels** - Use descriptive, user-friendly names
3. **Progressive Disclosure** - Show advanced options conditionally
4. **Visual Hierarchy** - Group related fields together

### **Formula Design:**
1. **Start Simple** - Build complexity gradually
2. **Test Thoroughly** - Verify all calculation paths
3. **Document Logic** - Add comments for complex formulas
4. **Error Handling** - Use min/max functions for bounds

### **User Experience:**
1. **Real-time Feedback** - Enable auto-calculation
2. **Clear Results** - Show breakdown of costs
3. **Mobile Friendly** - Test on all devices
4. **Accessibility** - Use proper labels and ARIA attributes

This plugin is now production-ready with professional features, beautiful UI, and robust functionality!
