<?php
/**
 * CFB Calculator - Variables Table Migration Script
 * 
 * This script migrates the variables table to support larger values
 * Run this once to update existing installations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if running directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>🔧 CFB Calculator - Variables Table Migration</h1>";

// Load the database class
if (!class_exists('CFB_Database')) {
    require_once('includes/class-cfb-database.php');
}

global $wpdb;

$table_name = $wpdb->prefix . 'cfb_variables';

echo "<h2>📊 Current Status</h2>";

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");

if (!$table_exists) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 6px; color: #856404;'>";
    echo "⚠️ Variables table doesn't exist yet. No migration needed.";
    echo "</div>";
    exit;
}

// Check current column definition
$column_info = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'value'");

if (!$column_info) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 6px; color: #721c24;'>";
    echo "❌ Error: Could not find 'value' column in variables table.";
    echo "</div>";
    exit;
}

echo "<p><strong>Current column definition:</strong> {$column_info->Type}</p>";

if (strpos($column_info->Type, 'decimal(10,4)') !== false) {
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 6px; color: #0c5460;'>";
    echo "🔄 Migration needed: Current limit is 999,999.9999";
    echo "</div>";
    
    echo "<h2>🚀 Running Migration</h2>";
    
    // Perform the migration
    $sql = "ALTER TABLE $table_name MODIFY COLUMN value decimal(20,4) NOT NULL DEFAULT 0";
    $result = $wpdb->query($sql);
    
    if ($result !== false) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 6px; color: #155724;'>";
        echo "✅ <strong>Migration successful!</strong><br>";
        echo "Variables can now store values up to 9,999,999,999,999,999.9999";
        echo "</div>";
        
        // Verify the change
        $new_column_info = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'value'");
        echo "<p><strong>New column definition:</strong> {$new_column_info->Type}</p>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 6px; color: #721c24;'>";
        echo "❌ <strong>Migration failed!</strong><br>";
        echo "Error: " . $wpdb->last_error;
        echo "</div>";
    }
    
} elseif (strpos($column_info->Type, 'decimal(20,4)') !== false) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 6px; color: #155724;'>";
    echo "✅ <strong>Already migrated!</strong><br>";
    echo "Variables can store values up to 9,999,999,999,999,999.9999";
    echo "</div>";
    
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 6px; color: #856404;'>";
    echo "⚠️ <strong>Unknown column type:</strong> {$column_info->Type}<br>";
    echo "Manual review may be needed.";
    echo "</div>";
}

echo "<h2>📋 Summary</h2>";
echo "<ul>";
echo "<li><strong>Before:</strong> decimal(10,4) - Maximum: 999,999.9999</li>";
echo "<li><strong>After:</strong> decimal(20,4) - Maximum: 9,999,999,999,999,999.9999</li>";
echo "<li><strong>Decimal places:</strong> Still 4 (no change)</li>";
echo "</ul>";

echo "<h2>🧪 Test</h2>";
echo "<p>You can now test by:</p>";
echo "<ol>";
echo "<li>Go to <strong>CFB Calculator → Variables</strong></li>";
echo "<li>Create or edit a variable</li>";
echo "<li>Try entering a large value like <strong>5,000,000</strong></li>";
echo "<li>It should save without being truncated to 1,000,000</li>";
echo "</ol>";

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 6px; color: #1565c0; margin-top: 20px;'>";
echo "💡 <strong>Note:</strong> This migration is automatically run when the plugin creates tables, ";
echo "so new installations won't need this script.";
echo "</div>";
?>
