<!DOCTYPE html>
<html>
<head>
    <title>CFB Variables & Dropdown Test v1.0.4</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        
        /* Formula Builder Layout */
        .cfb-formula-workspace { 
            display: grid; 
            grid-template-columns: 1fr 300px; 
            gap: 0; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            overflow: hidden;
            background: white;
            margin-bottom: 20px;
        }
        
        .cfb-formula-editor { 
            padding: 20px; 
            background: #fafafa; 
        }
        
        .cfb-formula-input { 
            width: 100%; 
            min-height: 100px; 
            padding: 12px; 
            border: 2px solid #e1e1e1; 
            border-radius: 6px; 
            font-family: 'Courier New', monospace; 
            font-size: 14px;
            resize: vertical;
        }
        
        /* Operations Bar */
        .cfb-operations-bar {
            margin-top: 16px;
            padding: 16px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .cfb-operations-section {
            margin-bottom: 16px;
        }
        
        .cfb-operations-section:last-child {
            margin-bottom: 0;
        }
        
        .cfb-operations-section h5 {
            margin: 0 0 12px 0;
            font-size: 12px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding-bottom: 6px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .cfb-operators-grid, .cfb-functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
            gap: 8px;
        }
        
        .cfb-operator-btn, .cfb-function-btn {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s ease;
            font-size: 13px;
            font-weight: 500;
        }
        
        .cfb-operator-btn:hover, .cfb-function-btn:hover {
            background: #e3f2fd;
            border-color: #2196F3;
            transform: translateY(-1px);
        }
        
        /* Right Sidebar */
        .cfb-formula-tools { 
            background: #fff; 
            border-left: 1px solid #e1e1e1; 
            padding: 20px; 
            overflow-y: auto;
        }
        
        .cfb-tool-section { 
            margin-bottom: 20px; 
        }
        
        .cfb-tool-section h5 { 
            margin: 0 0 12px 0; 
            font-size: 12px; 
            font-weight: 600; 
            color: #2c3e50; 
            text-transform: uppercase; 
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .field-item, .variable-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
        }
        
        .field-item:hover {
            background: #e3f2fd;
            border-color: #2196F3;
        }
        
        /* Variables Styling */
        .variable-item {
            background: #e8f5e8;
            border-color: #4caf50;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .variable-item:hover {
            background: #c8e6c9;
            border-color: #388e3c;
        }
        
        .variable-name {
            font-weight: 600;
            color: #2e7d32;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .variable-value {
            font-size: 11px;
            color: #558b2f;
            font-weight: 500;
        }
        
        /* Dropdown Test */
        .dropdown-test {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .dropdown-test select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 CFB Variables & Dropdown Test v1.0.4</h1>
        
        <div class="success">
            <strong>✅ New Features Added!</strong><br>
            • Variables now available in total field formulas<br>
            • Dropdown options show clean labels (no price clutter)<br>
            • Beautiful green styling for variables<br>
            • AJAX loading with smooth animations
        </div>
        
        <h2>1. 📊 New Formula Builder with Variables:</h2>
        
        <div class="cfb-formula-workspace">
            <div class="cfb-formula-editor">
                <label><strong>Formula:</strong></label>
                <textarea class="cfb-formula-input" placeholder="Click variables, fields, and operations below to build your formula...">{price} * {tax_rate} + {shipping_cost}</textarea>
                
                <div class="cfb-operations-bar">
                    <div class="cfb-operations-section">
                        <h5>🔧 Operators</h5>
                        <div class="cfb-operators-grid">
                            <button class="cfb-operator-btn" data-op="+">+</button>
                            <button class="cfb-operator-btn" data-op="-">-</button>
                            <button class="cfb-operator-btn" data-op="*">×</button>
                            <button class="cfb-operator-btn" data-op="/">÷</button>
                            <button class="cfb-operator-btn" data-op="(">(</button>
                            <button class="cfb-operator-btn" data-op=")">)</button>
                        </div>
                    </div>
                    
                    <div class="cfb-operations-section">
                        <h5>🧮 Functions</h5>
                        <div class="cfb-functions-grid">
                            <button class="cfb-function-btn" data-func="ceil">ceil()</button>
                            <button class="cfb-function-btn" data-func="floor">floor()</button>
                            <button class="cfb-function-btn" data-func="round">round()</button>
                            <button class="cfb-function-btn" data-func="min">min()</button>
                            <button class="cfb-function-btn" data-func="max">max()</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cfb-formula-tools">
                <div class="cfb-tool-section">
                    <h5>📋 Available Fields</h5>
                    <div class="field-item" data-field="price">💰 Price {price}</div>
                    <div class="field-item" data-field="quantity">📦 Quantity {quantity}</div>
                    <div class="field-item" data-field="size">📏 Size {size}</div>
                </div>
                
                <div class="cfb-tool-section">
                    <h5>⚙️ Variables (NEW!)</h5>
                    <div class="variable-item" data-variable="tax_rate">
                        <div class="variable-name">
                            <span style="color: #4caf50;">⚙️</span>
                            Tax Rate
                        </div>
                        <div class="variable-value">0.08 (8%)</div>
                    </div>
                    <div class="variable-item" data-variable="shipping_cost">
                        <div class="variable-name">
                            <span style="color: #4caf50;">⚙️</span>
                            Shipping Cost
                        </div>
                        <div class="variable-value">15.00</div>
                    </div>
                    <div class="variable-item" data-variable="handling_fee">
                        <div class="variable-name">
                            <span style="color: #4caf50;">⚙️</span>
                            Handling Fee
                        </div>
                        <div class="variable-value">5.00</div>
                    </div>
                </div>
            </div>
        </div>
        
        <h2>2. 🎯 Clean Dropdown Interface:</h2>
        
        <div class="dropdown-test">
            <h3>Before (Cluttered with Prices):</h3>
            <select disabled style="color: #999;">
                <option>Small ($10.00)</option>
                <option>Medium ($15.00)</option>
                <option>Large ($20.00)</option>
                <option>Extra Large ($25.00)</option>
            </select>
            
            <h3 style="margin-top: 20px;">After (Clean Labels Only):</h3>
            <select>
                <option value="">Select size</option>
                <option value="small" data-price="10.00">Small</option>
                <option value="medium" data-price="15.00">Medium</option>
                <option value="large" data-price="20.00">Large</option>
                <option value="xl" data-price="25.00">Extra Large</option>
            </select>
            
            <p style="margin-top: 10px; font-size: 13px; color: #6c757d;">
                <strong>Note:</strong> Prices are still stored in data-price attributes for calculations, 
                but the interface is now clean and professional.
            </p>
        </div>
        
        <div class="info">
            <h3>🎯 Key Improvements:</h3>
            <ul>
                <li><strong>Variables in Formulas:</strong> Global variables now available in total field formulas</li>
                <li><strong>Clean Dropdowns:</strong> No price clutter, just clean labels</li>
                <li><strong>Beautiful Styling:</strong> Green theme for variables, professional appearance</li>
                <li><strong>AJAX Loading:</strong> Variables loaded dynamically with smooth animations</li>
                <li><strong>Better UX:</strong> More intuitive workflow and cleaner interface</li>
            </ul>
        </div>
        
        <div class="success">
            <h3>🧪 Test Instructions:</h3>
            <ol>
                <li><strong>Clear browser cache:</strong> Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)</li>
                <li><strong>Create variables:</strong> Go to CFB Calculator → Variables</li>
                <li><strong>Test formula builder:</strong> Add Total field → Open formula builder</li>
                <li><strong>Verify variables:</strong> Should see Variables section with green styling</li>
                <li><strong>Test dropdowns:</strong> Create dropdown field → Check clean appearance</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Test functionality
        document.querySelectorAll('.cfb-operator-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const op = this.dataset.op;
                
                if (op === '(' || op === ')') {
                    textarea.value += op;
                } else {
                    textarea.value += ` ${op} `;
                }
                
                this.style.background = '#e3f2fd';
                setTimeout(() => this.style.background = '#fff', 200);
            });
        });
        
        document.querySelectorAll('.cfb-function-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const func = this.dataset.func;
                textarea.value += `${func}()`;
                
                this.style.background = '#e3f2fd';
                setTimeout(() => this.style.background = '#fff', 200);
            });
        });
        
        document.querySelectorAll('.field-item').forEach(item => {
            item.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const field = this.dataset.field;
                textarea.value += `{${field}}`;
                
                this.style.background = '#e3f2fd';
                setTimeout(() => this.style.background = '#f8f9fa', 200);
            });
        });
        
        document.querySelectorAll('.variable-item').forEach(item => {
            item.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const variable = this.dataset.variable;
                textarea.value += `{${variable}}`;
                
                this.style.background = '#c8e6c9';
                setTimeout(() => this.style.background = '#e8f5e8', 200);
            });
        });
        
        console.log('✅ Variables & Dropdown test loaded successfully!');
        console.log('🎉 Version 1.0.4 - Variables in formulas + Clean dropdowns!');
    </script>
</body>
</html>
