<?php
/**
 * Persian Font Installer for CFB Calculator
 * Helps install Persian fonts in TCPDF
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

// Load TCPDF
require_once(plugin_dir_path(__FILE__) . 'vendor/tecnickcom/tcpdf/tcpdf.php');

echo "<h1>CFB Calculator - Persian Font Installer</h1>";

// Check if TCPDF is available
if (!class_exists('TCPDF')) {
    echo "<p style='color: red;'>❌ TCPDF library not found!</p>";
    exit;
}

// Handle font installation
if (isset($_POST['install_fonts'])) {
    echo "<h2>Installing Persian Fonts...</h2>";
    
    $fonts_to_install = [
        'xyekan' => 'XYekan',
        'xnazanin' => 'XNazanin',
        'xzar' => 'XZar'
    ];
    
    foreach ($fonts_to_install as $font_key => $font_name) {
        echo "<h3>Installing $font_name ($font_key):</h3>";
        
        // Check if font files exist in plugin directory
        $plugin_font_dir = plugin_dir_path(__FILE__) . 'fonts/';
        $ttf_file = $plugin_font_dir . $font_key . '.ttf';
        
        if (file_exists($ttf_file)) {
            echo "<p>✅ Found TTF file: $ttf_file</p>";
            
            try {
                // Use TCPDF font converter
                $font_converter = new TCPDF_FONTS();
                
                // Convert TTF to TCPDF format
                $font_file = $font_converter->addTTFfont($ttf_file, 'TrueTypeUnicode', '', 96);
                
                if ($font_file) {
                    echo "<p style='color: green;'>✅ $font_name installed successfully!</p>";
                    echo "<p>Font file: $font_file</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to install $font_name</p>";
                }
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error installing $font_name: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ TTF file not found: $ttf_file</p>";
            echo "<p>Please copy the $font_key.ttf file to the fonts directory.</p>";
        }
        
        echo "<hr>";
    }
}

echo "<h2>Font Installation Status:</h2>";

$persian_fonts = [
    'xyekan' => 'XYekan',
    'xnazanin' => 'XNazanin', 
    'xzar' => 'XZar'
];

$all_installed = true;

foreach ($persian_fonts as $font_key => $font_name) {
    $font_path = K_PATH_FONTS . $font_key . '.php';
    $font_exists = file_exists($font_path);
    
    if (!$font_exists) {
        $all_installed = false;
    }
    
    $status = $font_exists ? '✅ Installed' : '❌ Not Installed';
    $color = $font_exists ? 'green' : 'red';
    
    echo "<p style='color: $color;'><strong>$font_name:</strong> $status</p>";
}

if (!$all_installed) {
    echo "<h2>Install Persian Fonts:</h2>";
    echo "<div style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd; border-radius: 4px;'>";
    echo "<p><strong>Step 1:</strong> Copy your Persian font TTF files to the plugin fonts directory:</p>";
    echo "<p><code>" . plugin_dir_path(__FILE__) . "fonts/</code></p>";
    echo "<p><strong>Required files:</strong></p>";
    echo "<ul>";
    echo "<li>xyekan.ttf</li>";
    echo "<li>xnazanin.ttf</li>";
    echo "<li>xzar.ttf</li>";
    echo "</ul>";
    echo "<p><strong>Step 2:</strong> Click the button below to install the fonts:</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='install_fonts' style='background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Install Persian Fonts</button>";
    echo "</form>";
    echo "</div>";
} else {
    echo "<p style='color: green; font-size: 16px;'><strong>✅ All Persian fonts are installed!</strong></p>";
}

echo "<h2>Manual Installation Instructions:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 4px;'>";
echo "<p><strong>If automatic installation fails, follow these steps:</strong></p>";
echo "<ol>";
echo "<li><strong>Download Persian fonts:</strong> Get XYekan, XNazanin, and XZar TTF files</li>";
echo "<li><strong>Convert fonts:</strong> Use TCPDF's font converter or online tools</li>";
echo "<li><strong>Copy files:</strong> Place the generated .php and .z files in TCPDF fonts directory</li>";
echo "<li><strong>Test:</strong> Use the font test script to verify installation</li>";
echo "</ol>";
echo "</div>";

echo "<h2>TCPDF Font Directory Info:</h2>";
echo "<p><strong>TCPDF Fonts Path:</strong> " . K_PATH_FONTS . "</p>";
echo "<p><strong>Plugin Fonts Path:</strong> " . plugin_dir_path(__FILE__) . "fonts/</p>";
echo "<p><strong>Directory Writable:</strong> " . (is_writable(K_PATH_FONTS) ? '✅ Yes' : '❌ No') . "</p>";

if (!is_writable(K_PATH_FONTS)) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
    echo "<p><strong>⚠️ Warning:</strong> TCPDF fonts directory is not writable!</p>";
    echo "<p>Please set proper permissions for: <code>" . K_PATH_FONTS . "</code></p>";
    echo "<p>Recommended permissions: 755 or 775</p>";
    echo "</div>";
}

echo "<h2>Alternative Font Sources:</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 4px;'>";
echo "<p><strong>Where to get Persian fonts:</strong></p>";
echo "<ul>";
echo "<li><strong>XYekan:</strong> Modern Persian font with good Unicode support</li>";
echo "<li><strong>XNazanin:</strong> Traditional Persian calligraphy style</li>";
echo "<li><strong>XZar:</strong> Clean, readable Persian font</li>";
echo "</ul>";
echo "<p><strong>Note:</strong> Make sure you have proper licensing for the fonts you use.</p>";
echo "</div>";

echo "<h2>Test Links:</h2>";
echo "<p>";
echo "<a href='test-persian-fonts.php' target='_blank' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>🧪 Test Persian Fonts</a>";
echo "<a href='admin.php?page=cfb-calculator-settings' style='background: #0073aa; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>⚙️ CFB Settings</a>";
echo "</p>";

// Check plugin fonts directory
$plugin_fonts_dir = plugin_dir_path(__FILE__) . 'fonts/';
echo "<h2>Plugin Fonts Directory:</h2>";
echo "<p><strong>Path:</strong> $plugin_fonts_dir</p>";
echo "<p><strong>Exists:</strong> " . (is_dir($plugin_fonts_dir) ? '✅ Yes' : '❌ No') . "</p>";

if (is_dir($plugin_fonts_dir)) {
    $ttf_files = glob($plugin_fonts_dir . '*.ttf');
    if (!empty($ttf_files)) {
        echo "<p><strong>TTF Files Found:</strong></p>";
        echo "<ul>";
        foreach ($ttf_files as $ttf_file) {
            $filename = basename($ttf_file);
            echo "<li>$filename</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ No TTF files found in plugin fonts directory</p>";
        echo "<p>Please copy your Persian font TTF files to: <code>$plugin_fonts_dir</code></p>";
    }
} else {
    echo "<p style='color: red;'>❌ Plugin fonts directory does not exist</p>";
    echo "<p>Creating directory...</p>";
    if (wp_mkdir_p($plugin_fonts_dir)) {
        echo "<p style='color: green;'>✅ Directory created successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create directory</p>";
    }
}
?>
