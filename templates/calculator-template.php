<?php
/**
 * CFB Calculator Template
 * 
 * This template can be copied to your theme's directory to customize the calculator display.
 * Copy to: your-theme/cfb-calculator/calculator-template.php
 * 
 * Available variables:
 * $form - Form object with all form data
 * $form_data - Decoded form configuration
 * $settings - Form settings
 * $show_title - Whether to show the form title
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get RTL direction
$is_rtl = is_rtl() || (isset($settings['rtl_mode']) && $settings['rtl_mode']);
$dir_attr = $is_rtl ? 'dir="rtl"' : 'dir="ltr"';
?>

<div class="cfb-calculator-wrapper cfb-theme-<?php echo esc_attr($settings['theme'] ?? 'modern'); ?>" 
     data-form-id="<?php echo esc_attr($form->id); ?>" 
     <?php echo $dir_attr; ?>>
     
    <?php if ($show_title && !empty($form->name)): ?>
        <div class="cfb-calculator-header">
            <h3 class="cfb-calculator-title"><?php echo esc_html($form->name); ?></h3>
            <?php if (!empty($form->description)): ?>
                <div class="cfb-calculator-description">
                    <?php echo wp_kses_post($form->description); ?>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <form class="cfb-calculator-form" id="cfb-form-<?php echo esc_attr($form->id); ?>">
        
        <?php if (isset($form_data['fields']) && is_array($form_data['fields'])): ?>
            <div class="cfb-form-fields">
                <?php foreach ($form_data['fields'] as $field): ?>
                    <?php $this->render_field_template($field); ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($form_data['enable_subtotals']) && $form_data['enable_subtotals']): ?>
            <div class="cfb-calculation-results">
                
                <?php if (isset($form_data['subtotals']) && is_array($form_data['subtotals'])): ?>
                    <div class="cfb-subtotals">
                        <h4 class="cfb-subtotals-title">
                            <?php _e('Price Breakdown', 'cfb-calculator'); ?>
                        </h4>
                        
                        <div class="cfb-subtotals-list">
                            <?php foreach ($form_data['subtotals'] as $index => $subtotal): ?>
                                <div class="cfb-subtotal-line" data-subtotal="<?php echo esc_attr($index); ?>">
                                    <span class="cfb-subtotal-label">
                                        <?php echo esc_html($subtotal['label']); ?>
                                    </span>
                                    <span class="cfb-subtotal-value" data-value="0">
                                        <span class="cfb-currency-symbol"><?php echo esc_html($settings['currency_symbol'] ?? '$'); ?></span>
                                        <span class="cfb-amount">0.00</span>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="cfb-total-section">
                    <div class="cfb-total-line">
                        <span class="cfb-total-label">
                            <?php _e('Total Price', 'cfb-calculator'); ?>
                        </span>
                        <span class="cfb-total-value" data-value="0">
                            <span class="cfb-currency-symbol"><?php echo esc_html($settings['currency_symbol'] ?? '$'); ?></span>
                            <span class="cfb-amount">0.00</span>
                        </span>
                    </div>
                </div>
                
            </div>
        <?php endif; ?>
        
        <div class="cfb-form-actions">
            <?php if (!($settings['auto_calculate'] ?? true)): ?>
                <button type="button" class="cfb-calculate-btn cfb-btn cfb-btn-primary">
                    <span class="cfb-btn-text"><?php _e('Calculate Price', 'cfb-calculator'); ?></span>
                    <span class="cfb-btn-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    </span>
                </button>
            <?php endif; ?>
            
            <button type="button" class="cfb-reset-btn cfb-btn cfb-btn-secondary">
                <span class="cfb-btn-text"><?php _e('Reset Form', 'cfb-calculator'); ?></span>
                <span class="cfb-btn-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                </span>
            </button>
        </div>
        
        <!-- Loading State -->
        <div class="cfb-loading" style="display: none;">
            <div class="cfb-loading-spinner">
                <svg class="cfb-spinner" width="24" height="24" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-dasharray="32" stroke-dashoffset="32">
                        <animate attributeName="stroke-dasharray" dur="2s" values="0 32;16 16;0 32;0 32" repeatCount="indefinite"/>
                        <animate attributeName="stroke-dashoffset" dur="2s" values="0;-16;-32;-32" repeatCount="indefinite"/>
                    </circle>
                </svg>
            </div>
            <span class="cfb-loading-text"><?php _e('Calculating...', 'cfb-calculator'); ?></span>
        </div>
        
        <!-- Error Message -->
        <div class="cfb-error-message" style="display: none;">
            <div class="cfb-error-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>
            <span class="cfb-error-text"></span>
        </div>
        
    </form>
    
    <?php if (isset($settings['show_powered_by']) && $settings['show_powered_by']): ?>
        <div class="cfb-powered-by">
            <small><?php _e('Powered by', 'cfb-calculator'); ?> <a href="#" target="_blank">CFB Calculator</a></small>
        </div>
    <?php endif; ?>
    
</div>

<?php
/**
 * Custom field template rendering
 * Override this method in your theme to customize field rendering
 */
function render_field_template($field) {
    $field_id = 'cfb-field-' . $field['name'];
    $field_class = 'cfb-field cfb-field-' . $field['type'];
    
    // Add conditional logic data attributes
    $conditional_attrs = '';
    if (isset($field['conditional_logic']) && $field['conditional_logic']['enabled']) {
        $conditional_attrs = 'data-conditional="' . esc_attr(wp_json_encode($field['conditional_logic'])) . '"';
    }
    
    // Add required class
    if (isset($field['required']) && $field['required']) {
        $field_class .= ' cfb-field-required';
    }
    ?>
    
    <div class="<?php echo esc_attr($field_class); ?>" <?php echo $conditional_attrs; ?>>
        
        <div class="cfb-field-header">
            <label for="<?php echo esc_attr($field_id); ?>" class="cfb-field-label">
                <span class="cfb-label-text"><?php echo esc_html($field['label']); ?></span>
                <?php if (isset($field['required']) && $field['required']): ?>
                    <span class="cfb-required-indicator" title="<?php _e('Required field', 'cfb-calculator'); ?>">*</span>
                <?php endif; ?>
            </label>
            
            <?php if (isset($field['help_text']) && !empty($field['help_text'])): ?>
                <div class="cfb-field-help">
                    <button type="button" class="cfb-help-trigger" aria-label="<?php _e('Help', 'cfb-calculator'); ?>">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                        </svg>
                    </button>
                    <div class="cfb-help-tooltip">
                        <?php echo wp_kses_post($field['help_text']); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="cfb-field-input">
            <?php render_field_input($field, $field_id); ?>
        </div>
        
        <?php if (isset($field['description']) && !empty($field['description'])): ?>
            <div class="cfb-field-description">
                <?php echo wp_kses_post($field['description']); ?>
            </div>
        <?php endif; ?>
        
    </div>
    
    <?php
}

/**
 * Render field input based on type
 * This function handles the actual input rendering
 */
function render_field_input($field, $field_id) {
    $field_name = $field['name'];
    $required = isset($field['required']) && $field['required'] ? 'required' : '';
    $classes = 'cfb-input cfb-input-' . $field['type'];
    
    switch ($field['type']) {
        case 'text':
            ?>
            <input type="text" 
                   id="<?php echo esc_attr($field_id); ?>" 
                   name="<?php echo esc_attr($field_name); ?>" 
                   placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                   class="<?php echo esc_attr($classes); ?>"
                   <?php echo $required; ?>>
            <?php
            break;
            
        case 'number':
            $min = isset($field['min']) ? 'min="' . esc_attr($field['min']) . '"' : '';
            $max = isset($field['max']) ? 'max="' . esc_attr($field['max']) . '"' : '';
            $step = isset($field['step']) ? 'step="' . esc_attr($field['step']) . '"' : '';
            $default = isset($field['default_value']) ? 'value="' . esc_attr($field['default_value']) . '"' : '';
            ?>
            <input type="number" 
                   id="<?php echo esc_attr($field_id); ?>" 
                   name="<?php echo esc_attr($field_name); ?>" 
                   class="<?php echo esc_attr($classes); ?>"
                   <?php echo $min . ' ' . $max . ' ' . $step . ' ' . $default . ' ' . $required; ?>>
            <?php
            break;
            
        case 'slider':
            $min = isset($field['min']) ? $field['min'] : 0;
            $max = isset($field['max']) ? $field['max'] : 100;
            $step = isset($field['step']) ? $field['step'] : 1;
            $default = isset($field['default_value']) ? $field['default_value'] : $min;
            $show_value = isset($field['show_value']) && $field['show_value'];
            ?>
            <div class="cfb-slider-container">
                <input type="range" 
                       id="<?php echo esc_attr($field_id); ?>" 
                       name="<?php echo esc_attr($field_name); ?>" 
                       min="<?php echo esc_attr($min); ?>"
                       max="<?php echo esc_attr($max); ?>"
                       step="<?php echo esc_attr($step); ?>"
                       value="<?php echo esc_attr($default); ?>"
                       class="<?php echo esc_attr($classes); ?>">
                       
                <?php if ($show_value): ?>
                    <div class="cfb-slider-display">
                        <div class="cfb-slider-value">
                            <span class="cfb-slider-current"><?php echo esc_html($default); ?></span>
                        </div>
                        <div class="cfb-slider-range">
                            <span class="cfb-range-min"><?php echo esc_html($min); ?></span>
                            <span class="cfb-range-max"><?php echo esc_html($max); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <?php
            break;
            
        case 'dropdown':
            ?>
            <select id="<?php echo esc_attr($field_id); ?>" 
                    name="<?php echo esc_attr($field_name); ?>" 
                    class="<?php echo esc_attr($classes); ?>"
                    <?php echo $required; ?>>
                    
                <?php if (isset($field['placeholder']) && !empty($field['placeholder'])): ?>
                    <option value=""><?php echo esc_html($field['placeholder']); ?></option>
                <?php endif; ?>
                
                <?php if (isset($field['options']) && is_array($field['options'])): ?>
                    <?php foreach ($field['options'] as $option): ?>
                        <option value="<?php echo esc_attr($option['value']); ?>">
                            <?php echo esc_html($option['label']); ?>
                        </option>
                    <?php endforeach; ?>
                <?php endif; ?>
            </select>
            <?php
            break;
            
        case 'radio':
            ?>
            <div class="cfb-radio-group">
                <?php if (isset($field['options']) && is_array($field['options'])): ?>
                    <?php foreach ($field['options'] as $index => $option): ?>
                        <label class="cfb-radio-option">
                            <input type="radio"
                                   name="<?php echo esc_attr($field_name); ?>"
                                   value="<?php echo esc_attr($option['value']); ?>"
                                   class="cfb-radio-input"
                                   <?php echo $required; ?>>
                            <span class="cfb-radio-indicator"></span>
                            <span class="cfb-radio-label">
                                <span class="cfb-option-text"><?php echo esc_html($option['label']); ?></span>
                            </span>
                        </label>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <?php
            break;
            
        case 'checkbox':
            ?>
            <div class="cfb-checkbox-group">
                <?php if (isset($field['options']) && is_array($field['options'])): ?>
                    <?php foreach ($field['options'] as $index => $option): ?>
                        <label class="cfb-checkbox-option">
                            <input type="checkbox"
                                   name="<?php echo esc_attr($field_name); ?>[]"
                                   value="<?php echo esc_attr($option['value']); ?>"
                                   class="cfb-checkbox-input">
                            <span class="cfb-checkbox-indicator"></span>
                            <span class="cfb-checkbox-label">
                                <span class="cfb-option-text"><?php echo esc_html($option['label']); ?></span>
                            </span>
                        </label>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            <?php
            break;
    }
}

/**
 * Format price for display
 */
function format_price($price) {
    $currency_symbol = get_option('cfb_currency_symbol', '$');
    $currency_position = get_option('cfb_currency_position', 'left');
    $decimal_places = get_option('cfb_decimal_places', 2);
    
    $formatted_price = number_format($price, $decimal_places);
    
    if ($currency_position === 'right') {
        return $formatted_price . ' ' . $currency_symbol;
    } else {
        return $currency_symbol . ' ' . $formatted_price;
    }
}
?>
