<?php
/**
 * Direct Formula Test - No WordPress Dependencies
 * Testing the formula engine directly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Direct Formula Engine Test</h1>";
echo "<p>Testing without WordPress dependencies</p>";

// Include the formula engine class directly
require_once('includes/class-cfb-formula-engine.php');

try {
    // Create a mock formula engine for testing
    class CFB_Formula_Engine_Test {
        private $variables = array();
        private $functions = array();
        
        public function __construct() {
            // Initialize supported functions
            $this->functions = array(
                'ceil' => 'ceil',
                'floor' => 'floor', 
                'round' => 'round',
                'abs' => 'abs',
                'max' => 'max',
                'min' => 'min',
                'sqrt' => 'sqrt',
                'pow' => 'pow'
            );
            
            // Set test variables
            $this->variables = array(
                'dropdown_4' => 6000,
                'price' => 10.50,
                'tax_rate' => 0.1
            );
        }
        
        public function evaluate_formula($formula) {
            echo "<strong>Testing formula:</strong> <code>$formula</code><br>";
            
            // Step 1: Replace variables
            $after_variables = $this->replace_variables($formula);
            echo "After variables: <code>$after_variables</code><br>";
            
            // Step 2: Replace functions
            $after_functions = $this->replace_functions($after_variables);
            echo "After functions: <code>$after_functions</code><br>";
            
            // Step 3: Evaluate
            $result = $this->safe_eval($after_functions);
            echo "Result: <strong>$result</strong><br><br>";
            
            return $result;
        }
        
        private function replace_variables($formula) {
            foreach ($this->variables as $name => $value) {
                $formula = str_replace('{' . $name . '}', $value, $formula);
            }
            return $formula;
        }
        
        private function replace_functions($formula) {
            $max_iterations = 20;
            $iteration = 0;
            
            while ($iteration < $max_iterations) {
                if (preg_match('/(\w+)\s*\(([^()]*)\)/', $formula, $matches)) {
                    $function_name = strtolower($matches[1]);
                    $params = $matches[2];
                    
                    if (isset($this->functions[$function_name])) {
                        $result = $this->call_function($function_name, $params);
                        $formula = str_replace($matches[0], $result, $formula);
                    } else {
                        $formula = str_replace($matches[0], '0', $formula);
                    }
                } else {
                    break;
                }
                $iteration++;
            }
            
            return $formula;
        }
        
        private function call_function($function_name, $params) {
            // Parse parameters
            $params = array_map('trim', explode(',', $params));
            $params = array_map('floatval', $params);
            
            switch ($function_name) {
                case 'ceil':
                    return ceil($params[0]);
                case 'floor':
                    return floor($params[0]);
                case 'round':
                    return round($params[0]);
                case 'abs':
                    return abs($params[0]);
                case 'max':
                    return max($params);
                case 'min':
                    return min($params);
                case 'sqrt':
                    return sqrt($params[0]);
                case 'pow':
                    return pow($params[0], $params[1]);
                default:
                    return 0;
            }
        }
        
        private function safe_eval($expression) {
            // Remove spaces
            $expression = str_replace(' ', '', $expression);
            
            // Validate only numbers and operators
            if (!preg_match('/^[0-9+\-*\/\(\)\.]+$/', $expression)) {
                return 0;
            }
            
            // Use eval safely (in production, use a proper math parser)
            try {
                $result = eval("return $expression;");
                return is_numeric($result) ? $result : 0;
            } catch (Exception $e) {
                return 0;
            }
        }
    }
    
    $engine = new CFB_Formula_Engine_Test();
    
    echo "<h2>Step-by-Step Testing:</h2>";
    
    // Test components of your formula
    $tests = array(
        '{dropdown_4}',
        '{dropdown_4} + 100',
        '{dropdown_4} + 100 - 5000',
        '({dropdown_4} + 100 - 5000) / 1000',
        'ceil(1.1)',
        'ceil(({dropdown_4} + 100 - 5000) / 1000)',
        'max(0, 2)',
        'max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))',
        '0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))',
        '1 + 0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))',
        '5000000 * (1 + 0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000)))'
    );
    
    foreach ($tests as $test) {
        $engine->evaluate_formula($test);
    }
    
    echo "<h2>Manual Calculation Verification:</h2>";
    echo "dropdown_4 = 6000<br>";
    echo "Step 1: 6000 + 100 - 5000 = 1100<br>";
    echo "Step 2: 1100 / 1000 = 1.1<br>";
    echo "Step 3: ceil(1.1) = 2<br>";
    echo "Step 4: max(0, 2) = 2<br>";
    echo "Step 5: 0.2 * 2 = 0.4<br>";
    echo "Step 6: 1 + 0.4 = 1.4<br>";
    echo "Step 7: 5000000 * 1.4 = 7000000<br>";
    echo "<strong>Expected Final Result: 7000000</strong>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage();
}
?>
