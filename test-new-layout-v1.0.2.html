<!DOCTYPE html>
<html>
<head>
    <title>CFB Formula Builder - New Layout Test v1.0.2</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        
        /* Simulate the new formula builder layout */
        .cfb-formula-workspace { 
            display: grid; 
            grid-template-columns: 1fr 280px; 
            gap: 0; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            overflow: hidden;
            background: white;
        }
        
        .cfb-formula-editor { 
            padding: 20px; 
            background: #fafafa; 
        }
        
        .cfb-formula-input { 
            width: 100%; 
            min-height: 100px; 
            padding: 12px; 
            border: 2px solid #e1e1e1; 
            border-radius: 6px; 
            font-family: 'Courier New', monospace; 
            font-size: 14px;
            resize: vertical;
        }
        
        /* New Operations Bar */
        .cfb-operations-bar {
            margin-top: 16px;
            padding: 16px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .cfb-operations-section {
            margin-bottom: 16px;
        }
        
        .cfb-operations-section:last-child {
            margin-bottom: 0;
        }
        
        .cfb-operations-section h5 {
            margin: 0 0 12px 0;
            font-size: 12px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding-bottom: 6px;
            border-bottom: 2px solid #dee2e6;
        }
        
        .cfb-operators-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
            gap: 8px;
        }
        
        .cfb-functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }
        
        .cfb-operator-btn, .cfb-function-btn {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s ease;
            font-size: 13px;
            font-weight: 500;
        }
        
        .cfb-operator-btn:hover, .cfb-function-btn:hover {
            background: #e3f2fd;
            border-color: #2196F3;
            transform: translateY(-1px);
        }
        
        .cfb-formula-tools { 
            background: #fff; 
            border-left: 1px solid #e1e1e1; 
            padding: 20px; 
        }
        
        .cfb-tool-section h5 { 
            margin: 0 0 12px 0; 
            font-size: 12px; 
            font-weight: 600; 
            color: #2c3e50; 
            text-transform: uppercase; 
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .field-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
        }
        
        .field-item:hover {
            background: #e3f2fd;
            border-color: #2196F3;
        }
        
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 6px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 CFB Formula Builder - New Layout v1.0.2</h1>
        
        <div class="success">
            <strong>✅ All Issues Fixed!</strong><br>
            • Functions moved under operations with beautiful UI<br>
            • RTL parentheses issue resolved<br>
            • "Unknown field type" error fixed<br>
            • Professional gradient design
        </div>
        
        <h2>New Formula Builder Layout:</h2>
        
        <div class="cfb-formula-workspace">
            <div class="cfb-formula-editor">
                <label><strong>Formula:</strong></label>
                <textarea class="cfb-formula-input" placeholder="Click fields and operations below to build your formula..."></textarea>
                
                <!-- NEW: Operations Bar with Operators + Functions -->
                <div class="cfb-operations-bar">
                    <div class="cfb-operations-section">
                        <h5>🔧 Operators</h5>
                        <div class="cfb-operators-grid">
                            <button class="cfb-operator-btn" data-op="+">+</button>
                            <button class="cfb-operator-btn" data-op="-">-</button>
                            <button class="cfb-operator-btn" data-op="*">×</button>
                            <button class="cfb-operator-btn" data-op="/">÷</button>
                            <button class="cfb-operator-btn" data-op="(">(</button>
                            <button class="cfb-operator-btn" data-op=")">)</button>
                            <button class="cfb-operator-btn" data-op=">">></button>
                            <button class="cfb-operator-btn" data-op="<"><</button>
                            <button class="cfb-operator-btn" data-op="==">=</button>
                            <button class="cfb-operator-btn" data-op="!=">≠</button>
                        </div>
                    </div>
                    
                    <div class="cfb-operations-section">
                        <h5>🧮 Functions</h5>
                        <div class="cfb-functions-grid">
                            <button class="cfb-function-btn" data-func="ceil">ceil()</button>
                            <button class="cfb-function-btn" data-func="floor">floor()</button>
                            <button class="cfb-function-btn" data-func="round">round()</button>
                            <button class="cfb-function-btn" data-func="min">min()</button>
                            <button class="cfb-function-btn" data-func="max">max()</button>
                            <button class="cfb-function-btn" data-func="abs">abs()</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cfb-formula-tools">
                <div class="cfb-tool-section">
                    <h5>📋 Available Fields</h5>
                    <div class="field-item" data-field="price">💰 Price {price}</div>
                    <div class="field-item" data-field="quantity">📦 Quantity {quantity}</div>
                    <div class="field-item" data-field="tax_rate">📊 Tax Rate {tax_rate}</div>
                    <div class="field-item" data-field="discount">🎯 Discount {discount}</div>
                </div>
            </div>
        </div>
        
        <div class="info">
            <h3>🎯 Key Improvements:</h3>
            <ul>
                <li><strong>Better Workflow:</strong> Formula → Operators → Functions → Fields</li>
                <li><strong>Beautiful Design:</strong> Gradient backgrounds and professional styling</li>
                <li><strong>RTL Fixed:</strong> Parentheses work correctly in all languages</li>
                <li><strong>More Space:</strong> Operations have full width instead of narrow sidebar</li>
                <li><strong>Clean Sidebar:</strong> Only fields in the right panel</li>
            </ul>
        </div>
        
        <div class="success">
            <h3>🧪 Test Instructions:</h3>
            <ol>
                <li><strong>Clear browser cache:</strong> Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)</li>
                <li><strong>Go to WordPress Admin:</strong> CFB Calculator → Add New Form</li>
                <li><strong>Add fields:</strong> Create some number/text fields</li>
                <li><strong>Add Total field:</strong> Open its settings</li>
                <li><strong>Verify layout:</strong> Should match the preview above</li>
            </ol>
        </div>
    </div>
    
    <script>
        // Test functionality
        document.querySelectorAll('.cfb-operator-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const op = this.dataset.op;
                
                // Test RTL parentheses fix
                if (op === '(' || op === ')') {
                    textarea.value += op; // No spaces for parentheses
                } else {
                    textarea.value += ` ${op} `;
                }
                
                this.style.background = '#e3f2fd';
                setTimeout(() => this.style.background = '#fff', 200);
            });
        });
        
        document.querySelectorAll('.cfb-function-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const func = this.dataset.func;
                textarea.value += `${func}()`;
                
                this.style.background = '#e3f2fd';
                setTimeout(() => this.style.background = '#fff', 200);
            });
        });
        
        document.querySelectorAll('.field-item').forEach(item => {
            item.addEventListener('click', function() {
                const textarea = document.querySelector('.cfb-formula-input');
                const field = this.dataset.field;
                textarea.value += `{${field}}`;
                
                this.style.background = '#e3f2fd';
                setTimeout(() => this.style.background = '#f8f9fa', 200);
            });
        });
        
        console.log('✅ New formula builder layout test loaded successfully!');
        console.log('🎉 Version 1.0.2 - All issues fixed!');
    </script>
</body>
</html>
