<?php
// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

// Test the nested formula directly
try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    
    // Set variables
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $property->setValue($formula_engine, array('dropdown_4' => 6000));
    
    // Test the formula
    $formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))';
    echo "Testing: $formula\n";
    
    $result = $formula_engine->evaluate_formula($formula);
    echo "Result: $result\n";
    
    if ($result == 7000000) {
        echo "SUCCESS: Formula working correctly!\n";
    } else {
        echo "FAILED: Expected 7000000, got $result\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

// Test AJAX
$_POST = array(
    'action' => 'cfb_calculate_price',
    'nonce' => wp_create_nonce('cfb_calculator_nonce'),
    'form_id' => 1,
    'form_data' => array('dropdown_4' => '6000')
);

ob_start();
$formula_engine->calculate_price();
$response = ob_get_clean();

echo "AJAX Response: $response\n";

$data = json_decode($response, true);
if ($data && $data['success'] && !empty($data['data']['calculations'])) {
    $calc_value = $data['data']['calculations'][0]['value'] ?? 0;
    if ($calc_value > 0) {
        echo "AJAX SUCCESS: Calculation value = $calc_value\n";
    } else {
        echo "AJAX FAILED: Calculation value = 0\n";
    }
} else {
    echo "AJAX FAILED: No calculations returned\n";
}
?>
