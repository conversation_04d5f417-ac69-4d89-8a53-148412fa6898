<?php
/**
 * Debug Frontend Calculation Issue
 * Trace exactly what happens when the frontend calculation button is clicked
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>🔍 Debug Frontend Calculation Issue</h1>";
echo "<p>Let's trace exactly what happens when you click the calculation button...</p>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. 📋 Check Your Form Configuration</h2>";

// Get the form you're testing with
global $wpdb;
$forms = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}cfb_forms ORDER BY id DESC LIMIT 5");

if (empty($forms)) {
    echo "<p style='color: red;'>❌ No forms found! You need to create a form first.</p>";
    exit;
}

echo "<h3>Available Forms:</h3>";
foreach ($forms as $form) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
    echo "<h4>Form ID: {$form->id} - {$form->name}</h4>";
    echo "<p><strong>Status:</strong> {$form->status}</p>";
    
    $form_data = json_decode($form->form_data, true);
    if ($form_data && isset($form_data['fields'])) {
        echo "<p><strong>Fields:</strong></p>";
        echo "<ul>";
        foreach ($form_data['fields'] as $field) {
            $type = $field['type'] ?? 'unknown';
            $name = $field['name'] ?? 'unnamed';
            $formula = isset($field['formula']) ? $field['formula'] : 'no formula';
            echo "<li><strong>{$name}</strong> ({$type})";
            if ($type === 'total' || $type === 'calculation') {
                echo " - Formula: <code>{$formula}</code>";
            }
            echo "</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
}

// Use Form ID 1 which has the calculation field
$test_form = null;
foreach ($forms as $form) {
    if ($form->id == 1) {
        $test_form = $form;
        break;
    }
}

if (!$test_form) {
    echo "<p style='color: red;'>❌ Form ID 1 not found! Using first available form.</p>";
    $test_form = $forms[0];
}
$form_data = json_decode($test_form->form_data, true);

echo "<h2>2. 🧪 Simulate Frontend AJAX Call</h2>";
echo "<p>Testing with Form ID: {$test_form->id}</p>";

// Simulate the exact AJAX call that the frontend makes
$_POST = array(
    'action' => 'cfb_calculate_price',
    'nonce' => wp_create_nonce('cfb_calculator_nonce'),
    'form_id' => $test_form->id,
    'form_data' => array(
        'dropdown_4' => '6000'  // Test with your dropdown value
    )
);

echo "<h3>Simulated POST Data:</h3>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

echo "<h3>AJAX Response:</h3>";

try {
    // Capture the AJAX response
    ob_start();
    
    // Call the AJAX handler directly
    $formula_engine = CFB_Formula_Engine::get_instance();
    $formula_engine->calculate_price();
    
    $ajax_response = ob_get_clean();
    
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 4px;'>";
    echo "<h4>Raw AJAX Response:</h4>";
    echo "<pre>" . htmlspecialchars($ajax_response) . "</pre>";
    echo "</div>";
    
    // Try to decode the JSON response
    $response_data = json_decode($ajax_response, true);
    
    if ($response_data) {
        echo "<h4>Parsed Response:</h4>";
        echo "<pre>" . print_r($response_data, true) . "</pre>";
        
        if (isset($response_data['success'])) {
            if ($response_data['success']) {
                echo "<p style='color: green; font-size: 18px;'>✅ <strong>AJAX call was successful!</strong></p>";
                
                if (isset($response_data['data']['calculations'])) {
                    echo "<h4>📊 Calculations Returned:</h4>";
                    foreach ($response_data['data']['calculations'] as $calc) {
                        $value = $calc['value'] ?? 0;
                        $formatted = $calc['formatted'] ?? 'no format';
                        $label = $calc['label'] ?? 'no label';
                        
                        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
                        echo "<strong>{$label}:</strong><br>";
                        echo "Value: {$value}<br>";
                        echo "Formatted: {$formatted}<br>";
                        
                        if ($value == 0) {
                            echo "<span style='color: red; font-weight: bold;'>⚠️ THIS IS WHY YOU SEE '---' - Value is 0!</span>";
                        }
                        echo "</div>";
                    }
                }
                
                if (isset($response_data['data']['total'])) {
                    $total = $response_data['data']['total'];
                    echo "<h4>💰 Total: {$total}</h4>";
                    if ($total == 0) {
                        echo "<p style='color: red; font-weight: bold;'>⚠️ Total is 0 - this causes '---' display!</p>";
                    }
                }
                
            } else {
                echo "<p style='color: red; font-size: 18px;'>❌ <strong>AJAX call failed!</strong></p>";
                if (isset($response_data['data'])) {
                    echo "<p>Error: {$response_data['data']}</p>";
                }
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠️ Could not parse JSON response</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
}

echo "<h2>3. 🔍 Check Frontend JavaScript Logic</h2>";

// Check the frontend JavaScript logic that handles the response
echo "<p>Let's examine what the frontend JavaScript does with the AJAX response...</p>";

echo "<h3>Frontend Logic Analysis:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 4px; border: 1px solid #ffeaa7;'>";
echo "<h4>🔍 The '---' Display Logic:</h4>";
echo "<p>In the frontend JavaScript, there are these conditions:</p>";
echo "<code>const displayValue = subtotal.value === 0 ? '---' : subtotal.formatted;</code><br>";
echo "<code>const displayValue = calculation.value === 0 ? '---' : this.formatCalculationValue(calculation.value, calculation.display_type);</code>";
echo "<br><br>";
echo "<p><strong>This means:</strong></p>";
echo "<ul>";
echo "<li>If <code>calculation.value</code> is <code>0</code>, display shows <code>'---'</code></li>";
echo "<li>If <code>calculation.value</code> is not <code>0</code>, display shows the formatted value</li>";
echo "</ul>";
echo "</div>";

echo "<h2>4. 🎯 Root Cause Analysis</h2>";

if (isset($response_data['data']['calculations'])) {
    $has_zero_values = false;
    foreach ($response_data['data']['calculations'] as $calc) {
        if (($calc['value'] ?? 0) == 0) {
            $has_zero_values = true;
            break;
        }
    }
    
    if ($has_zero_values) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 4px; border: 1px solid #f5c6cb;'>";
        echo "<h3 style='color: #721c24;'>🎯 ROOT CAUSE FOUND!</h3>";
        echo "<p><strong>The calculation is returning 0, which triggers the '---' display in the frontend.</strong></p>";
        echo "<p>The issue is NOT in the frontend JavaScript - it's working correctly.</p>";
        echo "<p>The issue is in the <strong>formula calculation</strong> returning 0 instead of the expected result.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 4px; border: 1px solid #c3e6cb;'>";
        echo "<h3 style='color: #155724;'>✅ Calculations Look Good!</h3>";
        echo "<p>The calculations are returning non-zero values, so the issue might be elsewhere.</p>";
        echo "</div>";
    }
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 4px; border: 1px solid #f5c6cb;'>";
    echo "<h3 style='color: #721c24;'>❌ NO CALCULATIONS RETURNED!</h3>";
    echo "<p>The AJAX response doesn't contain any calculations, which means the form processing failed.</p>";
    echo "</div>";
}

echo "<h2>5. 🔧 Next Steps to Fix</h2>";
echo "<ol>";
echo "<li><strong>Use the AI Settings page</strong> to test your formula directly</li>";
echo "<li><strong>Check the Variables Manager</strong> to ensure dropdown_4 variable exists</li>";
echo "<li><strong>Use the Formula Debugger</strong> to see step-by-step what's happening</li>";
echo "<li><strong>Check the Error Logs</strong> in the Debugging Tools tab</li>";
echo "<li><strong>Test with simpler formulas</strong> first to isolate the issue</li>";
echo "</ol>";

echo "<div style='border: 3px solid #007cba; padding: 20px; margin: 20px 0; background: #f0f8ff;'>";
echo "<h3 style='color: #007cba; margin: 0;'>🎯 Quick Fix Test</h3>";
echo "<p>Go to <strong>CFB Calculator → AI Settings → Formula Tester</strong> and test:</p>";
echo "<p><strong>Formula:</strong> <code>5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))</code></p>";
echo "<p><strong>Variables:</strong> <code>dropdown_4 = 6000</code></p>";
echo "<p>This will show you exactly what's wrong with the calculation.</p>";
echo "</div>";
?>
