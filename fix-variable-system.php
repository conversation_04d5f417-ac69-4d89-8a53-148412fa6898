<?php
/**
 * Fix Variable System
 * Comprehensive fix for the variable confusion between global variables and form fields
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>🔧 Fix Variable System</h1>";
echo "<p>Fixing the confusion between global variables and form field variables...</p>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

global $wpdb;

echo "<h2>1. 📊 Current Variable System Analysis</h2>";

// Check global variables
$global_vars = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}cfb_variables WHERE is_active = 1");
echo "<h3>Global Variables (from cfb_variables table):</h3>";
if (empty($global_vars)) {
    echo "<p style='color: orange;'>⚠️ No global variables found</p>";
} else {
    foreach ($global_vars as $var) {
        echo "<p>• <strong>{$var->name}:</strong> {$var->value} ({$var->label})</p>";
    }
}

// Check form fields
$form = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}cfb_forms WHERE id = 1");
if ($form) {
    $form_data = json_decode($form->form_data, true);
    echo "<h3>Form Fields (from form configuration):</h3>";
    if (isset($form_data['fields'])) {
        foreach ($form_data['fields'] as $field) {
            $type = $field['type'] ?? 'unknown';
            $name = $field['name'] ?? 'unnamed';
            echo "<p>• <strong>{$name}:</strong> {$type}</p>";
        }
    }
}

echo "<h2>2. 🎯 The Problem</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 4px; border: 1px solid #ffeaa7;'>";
echo "<h3>Variable System Confusion:</h3>";
echo "<p><strong>Global Variables:</strong> Stored in cfb_variables table (like {paper}, {print})</p>";
echo "<p><strong>Form Field Variables:</strong> Come from form field values (like {dropdown_4})</p>";
echo "<p><strong>The Issue:</strong> When you use {paper} in a formula, the system looks for a form field named 'paper' but doesn't find it.</p>";
echo "</div>";

echo "<h2>3. 🔧 Fix Implementation</h2>";

// Test the current formula engine behavior
try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    
    // Simulate the calculation process
    $_POST = array(
        'action' => 'cfb_calculate_price',
        'nonce' => wp_create_nonce('cfb_calculator_nonce'),
        'form_id' => 1,
        'form_data' => array(
            'dropdown_4' => '6000'
        )
    );
    
    echo "<h3>Testing Current Behavior:</h3>";
    
    // Capture the AJAX response
    ob_start();
    $formula_engine->calculate_price();
    $ajax_response = ob_get_clean();
    
    echo "<h4>AJAX Response:</h4>";
    echo "<pre>" . htmlspecialchars($ajax_response) . "</pre>";
    
    $response_data = json_decode($ajax_response, true);
    
    if ($response_data && $response_data['success']) {
        echo "<p style='color: green;'>✅ AJAX call successful</p>";
        
        if (isset($response_data['data']['variables'])) {
            echo "<h4>Variables Available During Calculation:</h4>";
            foreach ($response_data['data']['variables'] as $name => $value) {
                echo "<p>• <strong>{$name}:</strong> {$value}</p>";
            }
        }
        
        if (isset($response_data['data']['calculations'])) {
            echo "<h4>Calculations:</h4>";
            if (empty($response_data['data']['calculations'])) {
                echo "<p style='color: red;'>❌ No calculations returned</p>";
            } else {
                foreach ($response_data['data']['calculations'] as $calc) {
                    echo "<p>• <strong>{$calc['label']}:</strong> {$calc['value']} (formatted: {$calc['formatted']})</p>";
                }
            }
        }
    } else {
        echo "<p style='color: red;'>❌ AJAX call failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>4. 🎯 Solution Strategy</h2>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 4px; border: 1px solid #c3e6cb;'>";
echo "<h3>The Fix:</h3>";
echo "<ol>";
echo "<li><strong>Use Form Fields Only:</strong> Instead of global variables, create form fields for your values</li>";
echo "<li><strong>Hidden Fields:</strong> Create hidden form fields for constants like paper and print costs</li>";
echo "<li><strong>Consistent Variable Names:</strong> Use the same variable names in formulas as form field names</li>";
echo "</ol>";
echo "</div>";

echo "<h2>5. 🔧 Implementing the Fix</h2>";

// Let's update Form ID 1 to include the missing fields
if ($form) {
    $form_data = json_decode($form->form_data, true);
    
    // Check if paper and print fields exist
    $has_paper = false;
    $has_print = false;
    
    if (isset($form_data['fields'])) {
        foreach ($form_data['fields'] as $field) {
            if ($field['name'] === 'paper') $has_paper = true;
            if ($field['name'] === 'print') $has_print = true;
        }
    }
    
    echo "<h3>Current Form Fields Status:</h3>";
    echo "<p>• Paper field exists: " . ($has_paper ? "✅ Yes" : "❌ No") . "</p>";
    echo "<p>• Print field exists: " . ($has_print ? "✅ Yes" : "❌ No") . "</p>";
    
    if (!$has_paper || !$has_print) {
        echo "<h3>Adding Missing Fields:</h3>";
        
        // Add missing fields
        if (!isset($form_data['fields'])) {
            $form_data['fields'] = array();
        }
        
        if (!$has_paper) {
            $form_data['fields'][] = array(
                'type' => 'hidden',
                'name' => 'paper',
                'label' => 'Paper Cost',
                'value' => '105000',
                'default_value' => '105000'
            );
            echo "<p>✅ Added paper field (hidden, value: 105000)</p>";
        }
        
        if (!$has_print) {
            $form_data['fields'][] = array(
                'type' => 'hidden',
                'name' => 'print',
                'label' => 'Print Cost',
                'value' => '5000000',
                'default_value' => '5000000'
            );
            echo "<p>✅ Added print field (hidden, value: 5000000)</p>";
        }
        
        // Update the form in database
        $updated_form_data = wp_json_encode($form_data);
        $result = $wpdb->update(
            $wpdb->prefix . 'cfb_forms',
            array('form_data' => $updated_form_data),
            array('id' => 1),
            array('%s'),
            array('%d')
        );
        
        if ($result !== false) {
            echo "<p style='color: green;'>✅ Form updated successfully with missing fields</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update form: " . $wpdb->last_error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All required fields already exist in the form</p>";
    }
}

echo "<h2>6. 🧪 Test the Fix</h2>";

// Test again after the fix
try {
    echo "<h3>Testing After Fix:</h3>";
    
    // Simulate the calculation with all required fields
    $_POST = array(
        'action' => 'cfb_calculate_price',
        'nonce' => wp_create_nonce('cfb_calculator_nonce'),
        'form_id' => 1,
        'form_data' => array(
            'dropdown_4' => '6000',
            'paper' => '105000',
            'print' => '5000000'
        )
    );
    
    // Capture the AJAX response
    ob_start();
    $formula_engine = CFB_Formula_Engine::get_instance();
    $formula_engine->calculate_price();
    $ajax_response = ob_get_clean();
    
    echo "<h4>AJAX Response After Fix:</h4>";
    echo "<pre>" . htmlspecialchars($ajax_response) . "</pre>";
    
    $response_data = json_decode($ajax_response, true);
    
    if ($response_data && $response_data['success']) {
        echo "<p style='color: green; font-size: 18px;'>✅ AJAX call successful after fix!</p>";
        
        if (isset($response_data['data']['calculations']) && !empty($response_data['data']['calculations'])) {
            echo "<h4>✅ Calculations Now Working:</h4>";
            foreach ($response_data['data']['calculations'] as $calc) {
                $value = $calc['value'] ?? 0;
                echo "<p>• <strong>{$calc['label']}:</strong> " . number_format($value) . "</p>";
                
                if ($value > 0) {
                    echo "<p style='color: green; font-weight: bold;'>🎉 This will now show the calculated value instead of '---'!</p>";
                }
            }
        }
        
        $total = $response_data['data']['total'] ?? 0;
        if ($total > 0) {
            echo "<p style='color: green; font-size: 20px;'><strong>Total: " . number_format($total) . "</strong></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ AJAX call still failing</p>";
        if ($response_data && isset($response_data['data'])) {
            echo "<p>Error: " . $response_data['data'] . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>7. 🎯 Summary</h2>";

echo "<div style='border: 3px solid #28a745; padding: 20px; margin: 20px 0; background: #f8fff8;'>";
echo "<h3 style='color: #28a745; margin: 0;'>🎉 Variable System Fixed!</h3>";
echo "<p><strong>What was wrong:</strong> The formula referenced global variables {paper} and {print} but the system was looking for form fields.</p>";
echo "<p><strong>What was fixed:</strong> Added hidden form fields for paper and print so the formula can find them.</p>";
echo "<p><strong>Result:</strong> Your formula should now work correctly and show calculated values instead of '---'.</p>";
echo "</div>";

echo "<h2>8. 🚀 Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Test your frontend form</strong> - The calculation should now work</li>";
echo "<li><strong>Use form field names in formulas</strong> - Always use {field_name} that matches actual form fields</li>";
echo "<li><strong>Add hidden fields for constants</strong> - For fixed values like costs, use hidden form fields</li>";
echo "<li><strong>Use the AI Settings page</strong> for testing formulas before deployment</li>";
echo "</ol>";
?>
