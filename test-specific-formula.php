<?php
/**
 * Test Specific Formula Issue
 * Testing: 5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>CFB Calculator - Specific Formula Debug</h1>";
echo "<h2>Testing Formula: <code>5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))</code></h2>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "✅ Formula engine instance created<br>";
    
    // Test formula with different dropdown_4 values
    $test_values = array(1000, 2000, 3000, 4000, 5000, 6000, 10000);
    
    echo "<h3>Testing with different dropdown_4 values:</h3>";
    
    foreach ($test_values as $dropdown_value) {
        echo "<h4>Testing with dropdown_4 = $dropdown_value</h4>";
        
        // Set up variables manually for testing
        $reflection = new ReflectionClass($formula_engine);
        $property = $reflection->getProperty('variables');
        $property->setAccessible(true);
        $property->setValue($formula_engine, array('dropdown_4' => $dropdown_value));
        
        $original_formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))';
        
        echo "Original formula: <code>$original_formula</code><br>";
        
        // Step 1: Test variable replacement
        $replace_method = $reflection->getMethod('replace_variables');
        $replace_method->setAccessible(true);
        $after_variables = $replace_method->invoke($formula_engine, $original_formula);
        echo "After variable replacement: <code>$after_variables</code><br>";
        
        // Step 2: Test function replacement
        $replace_functions_method = $reflection->getMethod('replace_functions');
        $replace_functions_method->setAccessible(true);
        $after_functions = $replace_functions_method->invoke($formula_engine, $after_variables);
        echo "After function replacement: <code>$after_functions</code><br>";
        
        // Step 3: Test safe_eval
        $safe_eval_method = $reflection->getMethod('safe_eval');
        $safe_eval_method->setAccessible(true);
        
        try {
            $result = $safe_eval_method->invoke($formula_engine, $after_functions);
            echo "Final result: <strong>$result</strong><br>";
        } catch (Exception $e) {
            echo "❌ Error in safe_eval: " . $e->getMessage() . "<br>";
        }
        
        // Step 4: Test full evaluation
        try {
            $full_result = $formula_engine->evaluate_formula($original_formula);
            echo "Full evaluation result: <strong>$full_result</strong><br>";
        } catch (Exception $e) {
            echo "❌ Error in full evaluation: " . $e->getMessage() . "<br>";
        }
        
        echo "<hr>";
    }
    
    // Test individual components
    echo "<h3>Testing Individual Formula Components:</h3>";
    
    // Set dropdown_4 to 6000 for detailed testing
    $property->setValue($formula_engine, array('dropdown_4' => 6000));
    
    $test_components = array(
        '{dropdown_4}',
        '{dropdown_4}+100',
        '{dropdown_4}+100-5000',
        '({dropdown_4}+100-5000)/1000',
        'ceil(({dropdown_4}+100-5000)/1000)',
        'max(0,ceil(({dropdown_4}+100-5000)/1000))',
        '0.2*max(0,ceil(({dropdown_4}+100-5000)/1000))',
        '1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000))',
        '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))'
    );
    
    foreach ($test_components as $component) {
        echo "<strong>Component:</strong> <code>$component</code><br>";
        try {
            $result = $formula_engine->evaluate_formula($component);
            echo "Result: <strong>$result</strong><br>";
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "<br>";
        }
        echo "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<h3>Expected Calculation (Manual):</h3>";
echo "For dropdown_4 = 6000:<br>";
echo "1. {dropdown_4}+100-5000 = 6000+100-5000 = 1100<br>";
echo "2. 1100/1000 = 1.1<br>";
echo "3. ceil(1.1) = 2<br>";
echo "4. max(0,2) = 2<br>";
echo "5. 0.2*2 = 0.4<br>";
echo "6. 1+0.4 = 1.4<br>";
echo "7. 5000000*1.4 = 7000000<br>";
?>
