<?php
/**
 * Fix Calculation Issue
 * Comprehensive fix for the "---" display problem
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>🔧 Fix Calculation Issue</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>1. 📋 Analyze Form ID 1 (The one with your formula)</h2>";

global $wpdb;
$form = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}cfb_forms WHERE id = 1");

if (!$form) {
    echo "<p style='color: red;'>❌ Form ID 1 not found!</p>";
    exit;
}

echo "<h3>Form Details:</h3>";
echo "<p><strong>Name:</strong> {$form->name}</p>";
echo "<p><strong>Status:</strong> {$form->status}</p>";

$form_data = json_decode($form->form_data, true);
echo "<h3>Form Configuration:</h3>";
echo "<pre>" . print_r($form_data, true) . "</pre>";

// Check if the form has calculation fields
$calculation_fields = array();
if (isset($form_data['fields'])) {
    foreach ($form_data['fields'] as $field) {
        if (in_array($field['type'], ['total', 'calculation'])) {
            $calculation_fields[] = $field;
        }
    }
}

echo "<h3>Calculation Fields Found:</h3>";
if (empty($calculation_fields)) {
    echo "<p style='color: red;'>❌ No calculation fields found in this form!</p>";
} else {
    foreach ($calculation_fields as $field) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
        echo "<h4>Field: {$field['name']} ({$field['type']})</h4>";
        echo "<p><strong>Label:</strong> " . ($field['label'] ?? 'No label') . "</p>";
        echo "<p><strong>Formula:</strong> <code>" . ($field['formula'] ?? 'No formula') . "</code></p>";
        echo "</div>";
    }
}

echo "<h2>2. 🧪 Test Formula Engine Directly</h2>";

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "<p>✅ Formula engine created</p>";
    
    // Set up variables
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $property->setValue($formula_engine, array('dropdown_4' => 6000));
    
    // Test the exact formula from Form ID 1
    if (!empty($calculation_fields)) {
        foreach ($calculation_fields as $field) {
            if (isset($field['formula'])) {
                $formula = $field['formula'];
                echo "<h3>Testing Formula: <code>$formula</code></h3>";
                
                try {
                    $result = $formula_engine->evaluate_formula($formula);
                    echo "<p><strong>Result:</strong> <span style='color: green; font-size: 20px;'>$result</span></p>";
                    
                    if ($result == 0) {
                        echo "<p style='color: red;'>⚠️ This is why you see '---' - the formula returns 0!</p>";
                    } else {
                        echo "<p style='color: green;'>✅ Formula is working correctly!</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Formula error: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Formula engine error: " . $e->getMessage() . "</p>";
}

echo "<h2>3. 🔧 Test AJAX Handler</h2>";

// Test the AJAX handler with Form ID 1
$_POST = array(
    'action' => 'cfb_calculate_price',
    'nonce' => wp_create_nonce('cfb_calculator_nonce'),
    'form_id' => 1,
    'form_data' => array(
        'dropdown_4' => '6000'
    )
);

echo "<h3>AJAX Test with Form ID 1:</h3>";

try {
    ob_start();
    $formula_engine->calculate_price();
    $ajax_response = ob_get_clean();
    
    echo "<h4>Raw AJAX Response:</h4>";
    echo "<pre>" . htmlspecialchars($ajax_response) . "</pre>";
    
    $response_data = json_decode($ajax_response, true);
    if ($response_data) {
        echo "<h4>Parsed Response:</h4>";
        if ($response_data['success']) {
            echo "<p style='color: green;'>✅ AJAX Success</p>";
            echo "<p><strong>Calculations:</strong> " . count($response_data['data']['calculations']) . "</p>";
            echo "<p><strong>Total:</strong> " . $response_data['data']['total'] . "</p>";
            
            if (empty($response_data['data']['calculations'])) {
                echo "<p style='color: red;'>❌ No calculations returned - this is the problem!</p>";
            } elseif ($response_data['data']['total'] == 0) {
                echo "<p style='color: red;'>❌ Total is 0 - this causes '---' display!</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ AJAX Failed</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ AJAX Error: " . $e->getMessage() . "</p>";
}

echo "<h2>4. 🔍 Check Variables in Database</h2>";

$variables = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}cfb_variables");
if (empty($variables)) {
    echo "<p style='color: orange;'>⚠️ No variables found in database. Creating dropdown_4...</p>";
    
    // Create the dropdown_4 variable
    $result = $wpdb->insert(
        $wpdb->prefix . 'cfb_variables',
        array(
            'name' => 'dropdown_4',
            'label' => 'Dropdown 4',
            'value' => 6000,
            'is_active' => 1,
            'created_at' => current_time('mysql')
        ),
        array('%s', '%s', '%f', '%d', '%s')
    );
    
    if ($result) {
        echo "<p style='color: green;'>✅ Created dropdown_4 variable</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create dropdown_4 variable</p>";
    }
} else {
    echo "<h3>Variables in Database:</h3>";
    foreach ($variables as $var) {
        $status = $var->is_active ? 'Active' : 'Inactive';
        echo "<p><strong>{$var->name}:</strong> {$var->value} ({$status})</p>";
    }
}

echo "<h2>5. 🎯 Diagnosis & Fix</h2>";

// Check if dropdown_4 variable exists and is active
$dropdown_4_var = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}cfb_variables WHERE name = 'dropdown_4' AND is_active = 1");

if (!$dropdown_4_var) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 4px; border: 1px solid #f5c6cb;'>";
    echo "<h3 style='color: #721c24;'>🎯 ISSUE FOUND: Missing dropdown_4 Variable</h3>";
    echo "<p>The formula references {dropdown_4} but this variable doesn't exist in the database or is inactive.</p>";
    echo "<p><strong>Fix:</strong> Create the dropdown_4 variable in the Variables Manager.</p>";
    echo "</div>";
} else {
    echo "<p style='color: green;'>✅ dropdown_4 variable exists and is active</p>";
}

// Check if form has calculation fields
if (empty($calculation_fields)) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 4px; border: 1px solid #f5c6cb;'>";
    echo "<h3 style='color: #721c24;'>🎯 ISSUE FOUND: No Calculation Fields</h3>";
    echo "<p>Form ID 1 doesn't have any calculation or total fields configured.</p>";
    echo "<p><strong>Fix:</strong> Add a calculation field to your form with the formula.</p>";
    echo "</div>";
}

echo "<h2>6. 🚀 Quick Fix Actions</h2>";
echo "<ol>";
echo "<li><strong>Go to CFB Calculator → AI Settings</strong></li>";
echo "<li><strong>Use Variables Manager</strong> to ensure dropdown_4 exists</li>";
echo "<li><strong>Use Formula Tester</strong> to test your formula</li>";
echo "<li><strong>Check your form configuration</strong> to ensure it has calculation fields</li>";
echo "<li><strong>Test the frontend</strong> after making changes</li>";
echo "</ol>";

echo "<div style='border: 3px solid #007cba; padding: 20px; margin: 20px 0; background: #f0f8ff;'>";
echo "<h3 style='color: #007cba; margin: 0;'>🎯 Next Step</h3>";
echo "<p>Use the AI Settings page to debug and fix the formula step by step.</p>";
echo "<p><a href='" . admin_url('admin.php?page=cfb-calculator-ai-settings') . "' target='_blank'>Open AI Settings Page</a></p>";
echo "</div>";
?>
