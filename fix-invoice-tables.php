<?php
/**
 * Fix Invoice Tables
 * Manually create invoice tables and test functionality
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>CFB Invoice Tables Fix</h1>";

// Check if plugin is active
if (!class_exists('CFB_Calculator')) {
    echo "<p style='color: red;'>CFB Calculator plugin is not active!</p>";
    exit;
}

global $wpdb;

// Force create invoice tables
echo "<h2>Creating Invoice Tables...</h2>";

$charset_collate = $wpdb->get_charset_collate();

// Invoices table
$invoices_table = $wpdb->prefix . 'cfb_invoices';
$sql_invoices = "CREATE TABLE $invoices_table (
    id int(11) NOT NULL AUTO_INCREMENT,
    invoice_number varchar(50) NOT NULL,
    form_id int(11) NOT NULL,
    submission_id int(11),
    customer_name varchar(255) NOT NULL,
    customer_email varchar(255) NOT NULL,
    customer_phone varchar(50),
    customer_address text,
    subtotal decimal(10,2) NOT NULL DEFAULT 0,
    tax_amount decimal(10,2) NOT NULL DEFAULT 0,
    total_amount decimal(10,2) NOT NULL DEFAULT 0,
    currency varchar(10) DEFAULT 'USD',
    status varchar(20) DEFAULT 'draft',
    pdf_path varchar(500),
    notes text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY invoice_number (invoice_number),
    KEY form_id (form_id),
    KEY submission_id (submission_id),
    KEY status (status),
    KEY created_at (created_at)
) $charset_collate;";

// Invoice items table
$items_table = $wpdb->prefix . 'cfb_invoice_items';
$sql_items = "CREATE TABLE $items_table (
    id int(11) NOT NULL AUTO_INCREMENT,
    invoice_id int(11) NOT NULL,
    item_name varchar(255) NOT NULL,
    item_description text,
    quantity decimal(10,2) NOT NULL DEFAULT 1,
    unit_price decimal(10,2) NOT NULL DEFAULT 0,
    total_price decimal(10,2) NOT NULL DEFAULT 0,
    sort_order int(11) DEFAULT 0,
    PRIMARY KEY (id),
    KEY invoice_id (invoice_id),
    KEY sort_order (sort_order)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

echo "<p>Creating invoices table...</p>";
dbDelta($sql_invoices);

echo "<p>Creating invoice items table...</p>";
dbDelta($sql_items);

// Check if tables were created
$invoices_exists = $wpdb->get_var("SHOW TABLES LIKE '$invoices_table'");
$items_exists = $wpdb->get_var("SHOW TABLES LIKE '$items_table'");

echo "<p>Invoices table: " . ($invoices_exists ? '<span style="color: green;">CREATED</span>' : '<span style="color: red;">FAILED</span>') . "</p>";
echo "<p>Invoice items table: " . ($items_exists ? '<span style="color: green;">CREATED</span>' : '<span style="color: red;">FAILED</span>') . "</p>";

// Set default options if not set
echo "<h2>Setting Default Options...</h2>";

$options = array(
    'cfb_company_name' => get_bloginfo('name'),
    'cfb_company_email' => get_option('admin_email'),
    'cfb_pdf_template' => 'modern',
    'cfb_invoice_prefix' => 'INV',
    'cfb_payment_terms' => 'Payment is due within 30 days.'
);

foreach ($options as $option => $value) {
    if (!get_option($option)) {
        add_option($option, $value);
        echo "<p>Set $option = $value</p>";
    } else {
        echo "<p>$option already set</p>";
    }
}

// Test invoice creation directly
if ($invoices_exists) {
    echo "<h2>Testing Direct Invoice Creation...</h2>";
    
    // Generate test invoice number
    $invoice_number = 'TEST-' . date('Ym') . '-' . str_pad(1, 4, '0', STR_PAD_LEFT);
    
    $test_data = array(
        'invoice_number' => $invoice_number,
        'form_id' => 1,
        'submission_id' => null,
        'customer_name' => 'Test Customer',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '************',
        'customer_address' => 'Test Address',
        'subtotal' => 100.00,
        'tax_amount' => 10.00,
        'total_amount' => 110.00,
        'currency' => 'USD',
        'status' => 'draft',
        'notes' => 'Test invoice created by fix script'
    );
    
    $result = $wpdb->insert(
        $invoices_table,
        $test_data,
        array('%s', '%d', '%d', '%s', '%s', '%s', '%s', '%f', '%f', '%f', '%s', '%s', '%s')
    );
    
    if ($result) {
        $invoice_id = $wpdb->insert_id;
        echo "<p style='color: green;'>✓ Test invoice created successfully with ID: $invoice_id</p>";
        echo "<p>Invoice Number: $invoice_number</p>";
        
        // Test invoice retrieval
        $invoice = $wpdb->get_row($wpdb->prepare("SELECT * FROM $invoices_table WHERE id = %d", $invoice_id));
        if ($invoice) {
            echo "<p style='color: green;'>✓ Invoice retrieval successful</p>";
            echo "<pre>" . print_r($invoice, true) . "</pre>";
        }
        
        // Clean up test data
        $wpdb->delete($invoices_table, array('id' => $invoice_id), array('%d'));
        echo "<p>Test invoice cleaned up.</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create test invoice: " . $wpdb->last_error . "</p>";
    }
}

echo "<h2>Testing AJAX Handlers...</h2>";

// Check if AJAX actions are registered
global $wp_filter;
$actions_to_check = array(
    'wp_ajax_cfb_save_invoice',
    'wp_ajax_nopriv_cfb_save_invoice',
    'wp_ajax_cfb_generate_pdf',
    'wp_ajax_nopriv_cfb_generate_pdf'
);

foreach ($actions_to_check as $action) {
    $registered = isset($wp_filter[$action]) ? 'YES' : 'NO';
    $color = $registered === 'YES' ? 'green' : 'red';
    echo "<p>$action: <span style='color: $color;'>$registered</span></p>";
}

echo "<h2>Manual AJAX Test</h2>";
echo "<p>Use this form to test invoice creation:</p>";
?>

<form id="test-invoice-form" style="background: #f9f9f9; padding: 20px; border-radius: 5px;">
    <h3>Test Invoice Creation</h3>
    <table>
        <tr>
            <td><label>Customer Name:</label></td>
            <td><input type="text" id="customer_name" value="John Doe" required></td>
        </tr>
        <tr>
            <td><label>Customer Email:</label></td>
            <td><input type="email" id="customer_email" value="<EMAIL>" required></td>
        </tr>
        <tr>
            <td><label>Phone:</label></td>
            <td><input type="text" id="customer_phone" value="************"></td>
        </tr>
        <tr>
            <td><label>Address:</label></td>
            <td><textarea id="customer_address">123 Main St, City, State</textarea></td>
        </tr>
        <tr>
            <td><label>Total Amount:</label></td>
            <td><input type="number" id="total_amount" value="150.00" step="0.01"></td>
        </tr>
        <tr>
            <td colspan="2">
                <button type="button" id="test-invoice-btn">Create Test Invoice</button>
            </td>
        </tr>
    </table>
</form>

<div id="test-result" style="margin-top: 20px;"></div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
jQuery(document).ready(function($) {
    $('#test-invoice-btn').on('click', function() {
        const button = $(this);
        const resultDiv = $('#test-result');
        
        button.prop('disabled', true).text('Creating Invoice...');
        resultDiv.html('<p>Processing...</p>');
        
        const data = {
            action: 'cfb_save_invoice',
            nonce: '<?php echo wp_create_nonce('cfb_calculator_nonce'); ?>',
            customer_name: $('#customer_name').val(),
            customer_email: $('#customer_email').val(),
            customer_phone: $('#customer_phone').val(),
            customer_address: $('#customer_address').val(),
            form_id: 1,
            subtotal: parseFloat($('#total_amount').val()),
            tax_amount: 0,
            total_amount: parseFloat($('#total_amount').val())
        };
        
        console.log('Sending data:', data);
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: data,
            success: function(response) {
                console.log('Success response:', response);
                resultDiv.html('<div style="color: green;"><h4>Success!</h4><pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
            },
            error: function(xhr, status, error) {
                console.log('Error response:', xhr.responseText);
                resultDiv.html('<div style="color: red;"><h4>Error!</h4><p>Status: ' + status + '</p><p>Error: ' + error + '</p><p>Response: ' + xhr.responseText + '</p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('Create Test Invoice');
            }
        });
    });
});
</script>

<?php
echo "<h2>Debug Information</h2>";
echo "<p><strong>WordPress Version:</strong> " . get_bloginfo('version') . "</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Plugin Version:</strong> " . (defined('CFB_CALCULATOR_VERSION') ? CFB_CALCULATOR_VERSION : 'Unknown') . "</p>";
echo "<p><strong>Database Prefix:</strong> " . $wpdb->prefix . "</p>";
echo "<p><strong>AJAX URL:</strong> " . admin_url('admin-ajax.php') . "</p>";
echo "<p><strong>Current User ID:</strong> " . get_current_user_id() . "</p>";
echo "<p><strong>Current User Can Manage Options:</strong> " . (current_user_can('manage_options') ? 'YES' : 'NO') . "</p>";
?>
