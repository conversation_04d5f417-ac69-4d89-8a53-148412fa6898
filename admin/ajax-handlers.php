<?php
/**
 * AJAX Handlers for AI Settings Page
 * Handles variable management and formula debugging
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class CFB_AI_Settings_Ajax {
    
    public function __construct() {
        add_action('wp_ajax_cfb_add_variable', array($this, 'add_variable'));
        add_action('wp_ajax_cfb_toggle_variable', array($this, 'toggle_variable'));
        add_action('wp_ajax_cfb_delete_variable', array($this, 'delete_variable'));
        add_action('wp_ajax_cfb_debug_formula', array($this, 'debug_formula'));
        add_action('wp_ajax_cfb_test_formula', array($this, 'test_formula'));
    }
    
    /**
     * Add new variable
     */
    public function add_variable() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'cfb_variable_nonce')) {
                wp_send_json_error('Security check failed');
                return;
            }
            
            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }
            
            $name = sanitize_text_field($_POST['name']);
            $label = sanitize_text_field($_POST['label']);
            $value = floatval($_POST['value']);
            
            if (empty($name) || empty($label)) {
                wp_send_json_error('Name and label are required');
                return;
            }
            
            // Check if variable already exists
            global $wpdb;
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$wpdb->prefix}cfb_variables WHERE name = %s",
                $name
            ));
            
            if ($existing) {
                wp_send_json_error('Variable with this name already exists');
                return;
            }
            
            // Insert new variable
            $result = $wpdb->insert(
                $wpdb->prefix . 'cfb_variables',
                array(
                    'name' => $name,
                    'label' => $label,
                    'value' => $value,
                    'is_active' => 1,
                    'created_at' => current_time('mysql')
                ),
                array('%s', '%s', '%f', '%d', '%s')
            );
            
            if ($result) {
                wp_send_json_success('Variable added successfully');
            } else {
                wp_send_json_error('Failed to add variable');
            }
            
        } catch (Exception $e) {
            wp_send_json_error('Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Toggle variable active status
     */
    public function toggle_variable() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'cfb_variable_nonce')) {
                wp_send_json_error('Security check failed');
                return;
            }
            
            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }
            
            $id = intval($_POST['id']);
            
            global $wpdb;
            
            // Get current status
            $current_status = $wpdb->get_var($wpdb->prepare(
                "SELECT is_active FROM {$wpdb->prefix}cfb_variables WHERE id = %d",
                $id
            ));
            
            if ($current_status === null) {
                wp_send_json_error('Variable not found');
                return;
            }
            
            // Toggle status
            $new_status = $current_status ? 0 : 1;
            
            $result = $wpdb->update(
                $wpdb->prefix . 'cfb_variables',
                array('is_active' => $new_status),
                array('id' => $id),
                array('%d'),
                array('%d')
            );
            
            if ($result !== false) {
                wp_send_json_success('Variable status updated');
            } else {
                wp_send_json_error('Failed to update variable status');
            }
            
        } catch (Exception $e) {
            wp_send_json_error('Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete variable
     */
    public function delete_variable() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'cfb_variable_nonce')) {
                wp_send_json_error('Security check failed');
                return;
            }
            
            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }
            
            $id = intval($_POST['id']);
            
            global $wpdb;
            
            $result = $wpdb->delete(
                $wpdb->prefix . 'cfb_variables',
                array('id' => $id),
                array('%d')
            );
            
            if ($result) {
                wp_send_json_success('Variable deleted successfully');
            } else {
                wp_send_json_error('Failed to delete variable');
            }
            
        } catch (Exception $e) {
            wp_send_json_error('Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Debug formula with detailed information
     */
    public function debug_formula() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'cfb_debug_nonce')) {
                wp_send_json_error('Security check failed');
                return;
            }
            
            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }
            
            $formula = sanitize_text_field($_POST['formula']);
            
            if (empty($formula)) {
                wp_send_json_error('Formula is required');
                return;
            }
            
            $formula_engine = CFB_Formula_Engine::get_instance();
            
            // Load variables
            $reflection = new ReflectionClass($formula_engine);
            $property = $reflection->getProperty('variables');
            $property->setAccessible(true);
            
            // Get variables from database
            global $wpdb;
            $variables = $wpdb->get_results("SELECT name, value FROM {$wpdb->prefix}cfb_variables WHERE is_active = 1");
            $var_array = array();
            foreach ($variables as $var) {
                $var_array[$var->name] = floatval($var->value);
            }
            $property->setValue($formula_engine, $var_array);
            
            // Debug step by step
            $debug_info = array();
            
            // Step 1: Original formula
            $debug_info[] = "<strong>1. Original Formula:</strong><br><code>$formula</code>";
            
            // Step 2: Variable replacement
            $replace_vars_method = $reflection->getMethod('replace_variables');
            $replace_vars_method->setAccessible(true);
            $after_vars = $replace_vars_method->invoke($formula_engine, $formula);
            $debug_info[] = "<strong>2. After Variable Replacement:</strong><br><code>$after_vars</code>";
            
            // Step 3: Function replacement
            $replace_funcs_method = $reflection->getMethod('replace_functions');
            $replace_funcs_method->setAccessible(true);
            $after_funcs = $replace_funcs_method->invoke($formula_engine, $after_vars);
            $debug_info[] = "<strong>3. After Function Replacement:</strong><br><code>$after_funcs</code>";
            
            // Step 4: Final evaluation
            try {
                $result = $formula_engine->evaluate_formula($formula);
                $debug_info[] = "<strong>4. Final Result:</strong><br><span style='color: green; font-size: 18px; font-weight: bold;'>$result</span>";
            } catch (Exception $e) {
                $debug_info[] = "<strong>4. Evaluation Error:</strong><br><span style='color: red;'>" . $e->getMessage() . "</span>";
            }
            
            // Step 5: Variables used
            if (!empty($var_array)) {
                $vars_info = array();
                foreach ($var_array as $name => $value) {
                    $vars_info[] = "{$name} = $value";
                }
                $debug_info[] = "<strong>5. Variables Available:</strong><br>" . implode('<br>', $vars_info);
            }
            
            wp_send_json_success(implode('<br><br>', $debug_info));
            
        } catch (Exception $e) {
            wp_send_json_error('Debug error: ' . $e->getMessage());
        }
    }
    
    /**
     * Test formula (simple version for quick tests)
     */
    public function test_formula() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'cfb_debug_nonce')) {
                wp_send_json_error('Security check failed');
                return;
            }
            
            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }
            
            $formula = sanitize_text_field($_POST['formula']);
            
            if (empty($formula)) {
                wp_send_json_error('Formula is required');
                return;
            }
            
            $formula_engine = CFB_Formula_Engine::get_instance();
            
            // Load variables
            $reflection = new ReflectionClass($formula_engine);
            $property = $reflection->getProperty('variables');
            $property->setAccessible(true);
            
            // Get variables from database
            global $wpdb;
            $variables = $wpdb->get_results("SELECT name, value FROM {$wpdb->prefix}cfb_variables WHERE is_active = 1");
            $var_array = array();
            foreach ($variables as $var) {
                $var_array[$var->name] = floatval($var->value);
            }
            $property->setValue($formula_engine, $var_array);
            
            // Evaluate formula
            $result = $formula_engine->evaluate_formula($formula);
            wp_send_json_success(number_format($result, 2));
            
        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }
}

// Initialize AJAX handlers
new CFB_AI_Settings_Ajax();
?>
