<?php
/**
 * Clean AJAX Test
 * Test the AJAX calculation without HTML output interference
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting but don't display errors (they'll go to logs)
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

try {
    // Simulate the exact AJAX call for Form ID 1 (which has your formula)
    $_POST = array(
        'action' => 'cfb_calculate_price',
        'nonce' => wp_create_nonce('cfb_calculator_nonce'),
        'form_id' => 1, // Use Form ID 1 which has the calculation
        'form_data' => array(
            'dropdown_4' => '6000'
        )
    );

    // Capture the AJAX response
    ob_start();
    
    // Call the AJAX handler directly
    $formula_engine = CFB_Formula_Engine::get_instance();
    $formula_engine->calculate_price();
    
    $ajax_response = ob_get_clean();
    
    // Try to decode the JSON response
    $response_data = json_decode($ajax_response, true);
    
    if ($response_data) {
        // Return clean analysis
        $analysis = array(
            'ajax_success' => $response_data['success'] ?? false,
            'calculations_count' => count($response_data['data']['calculations'] ?? []),
            'total_value' => $response_data['data']['total'] ?? 0,
            'formatted_total' => $response_data['data']['formatted_total'] ?? 'none',
            'variables' => $response_data['data']['variables'] ?? [],
            'calculations' => $response_data['data']['calculations'] ?? [],
            'raw_response' => $ajax_response,
            'issue_found' => false,
            'issue_description' => ''
        );
        
        // Analyze the issue
        if ($analysis['ajax_success']) {
            if ($analysis['calculations_count'] == 0) {
                $analysis['issue_found'] = true;
                $analysis['issue_description'] = 'No calculations returned - form may not have calculation fields configured';
            } elseif ($analysis['total_value'] == 0) {
                $analysis['issue_found'] = true;
                $analysis['issue_description'] = 'Calculation returned 0 - this causes "---" display in frontend';
            } else {
                $analysis['issue_description'] = 'Calculation appears to be working correctly';
            }
        } else {
            $analysis['issue_found'] = true;
            $analysis['issue_description'] = 'AJAX call failed';
        }
        
        echo json_encode($analysis, JSON_PRETTY_PRINT);
    } else {
        // JSON decode failed
        echo json_encode(array(
            'error' => 'Could not parse AJAX response as JSON',
            'raw_response' => $ajax_response,
            'issue_found' => true,
            'issue_description' => 'AJAX response is not valid JSON'
        ), JSON_PRETTY_PRINT);
    }
    
} catch (Exception $e) {
    echo json_encode(array(
        'error' => $e->getMessage(),
        'issue_found' => true,
        'issue_description' => 'Exception occurred during calculation'
    ), JSON_PRETTY_PRINT);
}
?>
