<?php
/**
 * Simple Function Test
 * Test a very basic function to see if it works
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>Simple Function Test</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "✅ Formula engine created<br>";
    
    // Test very simple functions
    echo "<h2>Testing Simple Functions:</h2>";
    
    $simple_tests = array(
        'ceil(1.1)',
        'max(0, 2)',
        '2 + 3',
        'ceil(2.7)'
    );
    
    foreach ($simple_tests as $test) {
        echo "<h3>Testing: <code>$test</code></h3>";
        try {
            $result = $formula_engine->evaluate_formula($test);
            echo "Result: <strong style='color: green;'>$result</strong><br>";
        } catch (Exception $e) {
            echo "Error: <span style='color: red;'>" . $e->getMessage() . "</span><br>";
        }
        echo "<hr>";
    }
    
    // Test if functions are initialized
    echo "<h2>Checking Functions Array:</h2>";
    $reflection = new ReflectionClass($formula_engine);
    $functions_property = $reflection->getProperty('functions');
    $functions_property->setAccessible(true);
    $functions = $functions_property->getValue($formula_engine);
    
    echo "Functions available: " . count($functions) . "<br>";
    foreach ($functions as $name => $func) {
        echo "• $name<br>";
    }
    
    // Test calling a function directly
    echo "<h2>Testing Direct Function Call:</h2>";
    $call_function_method = $reflection->getMethod('call_function');
    $call_function_method->setAccessible(true);
    
    try {
        $result = $call_function_method->invoke($formula_engine, 'ceil', '1.1');
        echo "Direct call ceil(1.1) = $result<br>";
    } catch (Exception $e) {
        echo "Error calling function: " . $e->getMessage() . "<br>";
    }
    
    try {
        $result = $call_function_method->invoke($formula_engine, 'max', '0, 2');
        echo "Direct call max(0, 2) = $result<br>";
    } catch (Exception $e) {
        echo "Error calling function: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<h2>Check Error Logs</h2>";
echo "<p>Check your error logs for function replacement debugging information.</p>";
?>
