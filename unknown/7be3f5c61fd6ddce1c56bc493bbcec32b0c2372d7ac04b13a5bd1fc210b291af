<?php
/**
 * Test AI Settings Page
 * Quick test to verify the new AI settings page is accessible
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>🧪 Test AI Settings Page</h1>";

// Check if user is admin
if (!current_user_can('manage_options')) {
    echo "<p style='color: red;'>❌ You need admin privileges to access this test.</p>";
    echo "<p><a href='" . wp_login_url() . "'>Login as admin</a></p>";
    exit;
}

echo "<h2>✅ Admin Access Confirmed</h2>";

// Check if the AI settings page exists
$ai_settings_url = admin_url('admin.php?page=cfb-calculator-ai-settings');
echo "<h2>🔗 AI Settings Page URL:</h2>";
echo "<p><a href='$ai_settings_url' target='_blank'>$ai_settings_url</a></p>";

// Check if required files exist
$files_to_check = array(
    'admin/views/ai-settings-redesigned.php' => 'AI Settings Page View',
    'admin/ajax-handlers.php' => 'AJAX Handlers',
    'includes/class-cfb-formula-engine.php' => 'Formula Engine'
);

echo "<h2>📁 File Existence Check:</h2>";
foreach ($files_to_check as $file => $description) {
    $full_path = plugin_dir_path(__FILE__) . $file;
    if (file_exists($full_path)) {
        echo "<p>✅ <strong>$description:</strong> $file</p>";
    } else {
        echo "<p>❌ <strong>$description:</strong> $file (Missing!)</p>";
    }
}

// Check database tables
echo "<h2>🗄️ Database Tables Check:</h2>";
global $wpdb;

$tables_to_check = array(
    $wpdb->prefix . 'cfb_forms' => 'Forms Table',
    $wpdb->prefix . 'cfb_variables' => 'Variables Table',
    $wpdb->prefix . 'cfb_submissions' => 'Submissions Table'
);

foreach ($tables_to_check as $table => $description) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo "<p>✅ <strong>$description:</strong> $table ($count records)</p>";
    } else {
        echo "<p>❌ <strong>$description:</strong> $table (Missing!)</p>";
    }
}

// Check if formula engine is working
echo "<h2>🧮 Formula Engine Test:</h2>";
try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "<p>✅ Formula engine instance created successfully</p>";
    
    // Test basic calculation
    $test_result = $formula_engine->evaluate_formula('2 + 3');
    echo "<p>✅ Basic calculation test: 2 + 3 = $test_result</p>";
    
    // Test function
    $test_result2 = $formula_engine->evaluate_formula('max(5, 10)');
    echo "<p>✅ Function test: max(5, 10) = $test_result2</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Formula engine error: " . $e->getMessage() . "</p>";
}

// Check WordPress admin menu
echo "<h2>📋 WordPress Admin Menu:</h2>";
echo "<p>The AI Settings page should appear in the CFB Calculator admin menu.</p>";
echo "<p><strong>Navigation:</strong> WordPress Admin → CFB Calculator → AI Settings</p>";

// Test AJAX endpoints
echo "<h2>🔌 AJAX Endpoints Test:</h2>";
$ajax_actions = array(
    'cfb_add_variable',
    'cfb_toggle_variable', 
    'cfb_delete_variable',
    'cfb_debug_formula',
    'cfb_test_formula'
);

foreach ($ajax_actions as $action) {
    if (has_action("wp_ajax_$action")) {
        echo "<p>✅ AJAX action registered: $action</p>";
    } else {
        echo "<p>❌ AJAX action missing: $action</p>";
    }
}

echo "<h2>🎯 Next Steps:</h2>";
echo "<ol>";
echo "<li><strong>Access the AI Settings Page:</strong> <a href='$ai_settings_url' target='_blank'>Click here to open AI Settings</a></li>";
echo "<li><strong>Test Formula Builder:</strong> Try creating and testing formulas in the Formula Builder tab</li>";
echo "<li><strong>Test Variables Manager:</strong> Add, edit, and manage variables</li>";
echo "<li><strong>Use Debugging Tools:</strong> Debug your formulas step by step</li>";
echo "<li><strong>Test Your Original Formula:</strong> Enter your complex formula and see if it works now</li>";
echo "</ol>";

echo "<div style='border: 3px solid #0073aa; padding: 20px; margin: 20px 0; background: #f0f8ff;'>";
echo "<h2 style='color: #0073aa; margin: 0;'>🚀 Ready to Test!</h2>";
echo "<p style='margin: 10px 0 0 0;'>Your new AI Settings page with advanced formula builder is ready to use!</p>";
echo "</div>";
?>
