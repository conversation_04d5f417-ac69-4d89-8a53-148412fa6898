<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CFB Calculator - Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .calculate-btn {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        .calculate-btn:hover {
            background: #005a87;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .loading {
            display: none;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CFB Calculator - Frontend Calculation Test</h1>
        <p>This test simulates a frontend calculation to verify the parse_expression fix.</p>
        
        <form id="test-form">
            <div class="form-group">
                <label for="quantity">Quantity:</label>
                <input type="number" id="quantity" name="quantity" value="5" min="1" max="100">
            </div>
            
            <div class="form-group">
                <label for="price">Price per Unit:</label>
                <input type="number" id="price" name="price" value="10.50" step="0.01" min="0">
            </div>
            
            <div class="form-group">
                <label for="discount">Discount (%):</label>
                <input type="number" id="discount" name="discount" value="10" min="0" max="100">
            </div>
            
            <button type="button" class="calculate-btn" onclick="testCalculation()">Calculate Total</button>
            
            <div class="loading" id="loading">Calculating...</div>
        </form>
        
        <div id="results" class="results" style="display: none;">
            <h3>Calculation Results:</h3>
            <div id="result-content"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function testCalculation() {
            const quantity = document.getElementById('quantity').value;
            const price = document.getElementById('price').value;
            const discount = document.getElementById('discount').value;
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            
            // Prepare form data
            const formData = {
                quantity: quantity,
                price: price,
                discount: discount
            };
            
            // Make AJAX request to WordPress
            $.ajax({
                url: '/w1/wp-admin/admin-ajax.php',
                type: 'POST',
                data: {
                    action: 'cfb_calculate_price',
                    nonce: '<?php echo wp_create_nonce("cfb_calculator_nonce"); ?>',
                    form_id: 999, // Test form ID
                    form_data: formData
                },
                success: function(response) {
                    document.getElementById('loading').style.display = 'none';
                    
                    if (response.success) {
                        showResults(response.data, 'success');
                    } else {
                        showResults('Error: ' + (response.data || 'Calculation failed'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    document.getElementById('loading').style.display = 'none';
                    showResults('Network error: ' + error + '<br>Response: ' + xhr.responseText, 'error');
                }
            });
        }
        
        function showResults(data, type) {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('result-content');
            
            resultsDiv.className = 'results ' + type;
            
            if (typeof data === 'object') {
                contentDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } else {
                contentDiv.innerHTML = data;
            }
            
            resultsDiv.style.display = 'block';
        }
        
        // Test direct calculation without AJAX
        function testDirectCalculation() {
            const quantity = parseFloat(document.getElementById('quantity').value);
            const price = parseFloat(document.getElementById('price').value);
            const discount = parseFloat(document.getElementById('discount').value);
            
            // Simple calculation
            const subtotal = quantity * price;
            const discountAmount = subtotal * (discount / 100);
            const total = subtotal - discountAmount;
            
            const result = {
                subtotal: subtotal.toFixed(2),
                discount_amount: discountAmount.toFixed(2),
                total: total.toFixed(2),
                message: 'Direct JavaScript calculation (no server-side processing)'
            };
            
            showResults(result, 'success');
        }
    </script>
    
    <div style="margin-top: 20px; text-align: center;">
        <button type="button" class="calculate-btn" onclick="testDirectCalculation()" style="background: #28a745;">Test Direct Calculation</button>
        <p style="font-size: 14px; color: #6c757d; margin-top: 10px;">
            Use "Test Direct Calculation" for client-side testing without server communication.
        </p>
    </div>
</body>
</html>
