<?php
/**
 * Final Formula Test
 * Test the complete formula with all fixes applied
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Final Formula Test - All Fixes Applied</h1>";
echo "<h2>Testing: <code>5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))</code></h2>";

// Database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'w1';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>✅ Database Connected</h3>";
    
    // Ensure dropdown_4 variable exists
    $stmt = $pdo->prepare("SELECT * FROM wp_cfb_variables WHERE name = ?");
    $stmt->execute(['dropdown_4']);
    $dropdown_var = $stmt->fetch();
    
    if (!$dropdown_var) {
        $stmt = $pdo->prepare("INSERT INTO wp_cfb_variables (name, label, value, is_active) VALUES (?, ?, ?, ?)");
        $stmt->execute(['dropdown_4', 'Dropdown 4', '6000', 1]);
        echo "<h3>✅ Created dropdown_4 variable with value 6000</h3>";
    } else {
        echo "<h3>✅ dropdown_4 variable exists with value: " . $dropdown_var['value'] . "</h3>";
    }
    
    // Load WordPress and CFB classes
    require_once('wp-config.php');
    require_once('wp-load.php');
    
    echo "<h3>✅ WordPress Loaded</h3>";
    
    // Test the actual CFB Formula Engine
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "<h3>✅ CFB Formula Engine Created</h3>";
    
    // Test the complete formula
    $formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))';
    
    echo "<div style='border: 2px solid #007cba; padding: 20px; margin: 20px 0; background: #f8f9fa;'>";
    echo "<h3>🧮 Testing Complete Formula:</h3>";
    echo "<p><strong>Formula:</strong> <code>$formula</code></p>";
    
    try {
        $result = $formula_engine->evaluate_formula($formula);
        echo "<p><strong>Result:</strong> <span style='color: green; font-size: 24px; font-weight: bold;'>$result</span></p>";
        
        if ($result > 0) {
            echo "<p style='color: green;'>✅ <strong>SUCCESS!</strong> Formula calculated successfully!</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Formula returned 0 - check logs for details</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    // Manual verification
    $dropdown_value = $dropdown_var ? $dropdown_var['value'] : 6000;
    echo "<div style='border: 2px solid #28a745; padding: 20px; margin: 20px 0; background: #f8fff8;'>";
    echo "<h3>📊 Manual Verification:</h3>";
    echo "<p>Using dropdown_4 = $dropdown_value</p>";
    
    $step1 = $dropdown_value + 100 - 5000;
    echo "<p>Step 1: $dropdown_value + 100 - 5000 = <strong>$step1</strong></p>";
    
    $step2 = $step1 / 1000;
    echo "<p>Step 2: $step1 / 1000 = <strong>$step2</strong></p>";
    
    $step3 = ceil($step2);
    echo "<p>Step 3: ceil($step2) = <strong>$step3</strong></p>";
    
    $step4 = max(0, $step3);
    echo "<p>Step 4: max(0, $step3) = <strong>$step4</strong></p>";
    
    $step5 = 0.2 * $step4;
    echo "<p>Step 5: 0.2 * $step4 = <strong>$step5</strong></p>";
    
    $step6 = 1 + $step5;
    echo "<p>Step 6: 1 + $step5 = <strong>$step6</strong></p>";
    
    $final = 5000000 * $step6;
    echo "<p>Step 7: 5000000 * $step6 = <strong style='color: green; font-size: 20px;'>" . number_format($final) . "</strong></p>";
    
    echo "<h3 style='color: green;'>Expected Result: " . number_format($final) . "</h3>";
    echo "</div>";
    
    // Test with different dropdown values
    echo "<div style='border: 2px solid #6f42c1; padding: 20px; margin: 20px 0; background: #faf9ff;'>";
    echo "<h3>🔄 Testing with Different Values:</h3>";
    
    $test_values = [1000, 3000, 5000, 7000, 10000];
    
    foreach ($test_values as $test_value) {
        // Update the variable in database temporarily
        $stmt = $pdo->prepare("UPDATE wp_cfb_variables SET value = ? WHERE name = ?");
        $stmt->execute([$test_value, 'dropdown_4']);
        
        // Test the formula
        try {
            $result = $formula_engine->evaluate_formula($formula);
            
            // Calculate expected manually
            $expected_step1 = $test_value + 100 - 5000;
            $expected_step2 = $expected_step1 / 1000;
            $expected_step3 = ceil($expected_step2);
            $expected_step4 = max(0, $expected_step3);
            $expected_step5 = 0.2 * $expected_step4;
            $expected_step6 = 1 + $expected_step5;
            $expected_final = 5000000 * $expected_step6;
            
            $match = abs($result - $expected_final) < 0.01;
            $status = $match ? "✅" : "❌";
            
            echo "<p>$status dropdown_4 = $test_value → Result: <strong>$result</strong> (Expected: " . number_format($expected_final) . ")</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ dropdown_4 = $test_value → Error: " . $e->getMessage() . "</p>";
        }
    }
    
    // Restore original value
    $original_value = $dropdown_var ? $dropdown_var['value'] : 6000;
    $stmt = $pdo->prepare("UPDATE wp_cfb_variables SET value = ? WHERE name = ?");
    $stmt->execute([$original_value, 'dropdown_4']);
    
    echo "</div>";
    
    echo "<h2 style='color: green;'>🎉 Test Complete!</h2>";
    echo "<p>If you see calculated results above, the formula engine is working correctly.</p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error: " . $e->getMessage() . "</h3>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
