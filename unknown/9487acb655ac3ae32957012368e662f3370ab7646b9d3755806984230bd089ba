<?php
/**
 * Working Formula Test
 * Test the actual formula with real database data
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>CFB Formula Engine - Working Test</h1>";

// Database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'w1';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. Database Connection: ✅ Connected</h2>";
    
    // Load variables from database
    echo "<h2>2. Loading Variables from Database:</h2>";
    $stmt = $pdo->query("SELECT name, value FROM wp_cfb_variables WHERE is_active = 1");
    $db_variables = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    echo "Variables loaded: " . count($db_variables) . "<br>";
    foreach ($db_variables as $name => $value) {
        echo "• $name = $value<br>";
    }
    
    // Create a working formula engine
    echo "<h2>3. Testing Formula Engine:</h2>";
    
    class WorkingFormulaEngine {
        private $variables = array();
        
        public function __construct($variables) {
            $this->variables = $variables;
        }
        
        public function evaluate($formula) {
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Formula:</strong> <code>$formula</code><br>";
            
            // Step 1: Replace variables
            $step1 = $this->replaceVariables($formula);
            echo "<strong>After variables:</strong> <code>$step1</code><br>";
            
            // Step 2: Replace functions
            $step2 = $this->replaceFunctions($step1);
            echo "<strong>After functions:</strong> <code>$step2</code><br>";
            
            // Step 3: Evaluate
            $result = $this->safeEval($step2);
            echo "<strong>Result:</strong> <span style='color: green; font-size: 18px;'>$result</span><br>";
            echo "</div>";
            
            return $result;
        }
        
        private function replaceVariables($formula) {
            foreach ($this->variables as $name => $value) {
                $formula = str_replace('{' . $name . '}', $value, $formula);
            }
            return $formula;
        }
        
        private function replaceFunctions($formula) {
            // Handle nested functions from inside out
            $iterations = 0;
            while ($iterations < 20 && preg_match('/(\w+)\s*\(([^()]*)\)/', $formula, $matches)) {
                $func = strtolower($matches[1]);
                $params = array_map('trim', explode(',', $matches[2]));
                $params = array_map('floatval', $params);
                
                $result = 0;
                switch ($func) {
                    case 'ceil':
                        $result = ceil($params[0]);
                        break;
                    case 'floor':
                        $result = floor($params[0]);
                        break;
                    case 'round':
                        $result = round($params[0]);
                        break;
                    case 'max':
                        $result = max($params);
                        break;
                    case 'min':
                        $result = min($params);
                        break;
                    case 'abs':
                        $result = abs($params[0]);
                        break;
                    case 'sqrt':
                        $result = sqrt($params[0]);
                        break;
                }
                
                $formula = str_replace($matches[0], $result, $formula);
                $iterations++;
            }
            return $formula;
        }
        
        private function safeEval($expression) {
            // Clean expression
            $expression = str_replace(' ', '', $expression);
            
            // Validate
            if (!preg_match('/^[0-9+\-*\/\(\)\.]+$/', $expression)) {
                return "Invalid expression";
            }
            
            // Evaluate
            try {
                $result = eval("return $expression;");
                return is_numeric($result) ? number_format($result, 2) : 0;
            } catch (Exception $e) {
                return "Error: " . $e->getMessage();
            }
        }
    }
    
    // Test the formula engine
    $engine = new WorkingFormulaEngine($db_variables);
    
    echo "<h2>4. Testing Your Formula Step by Step:</h2>";
    
    // Test each component
    $tests = array(
        'Simple variable' => '{dropdown_4}',
        'Addition' => '{dropdown_4} + 100',
        'Subtraction' => '{dropdown_4} + 100 - 5000',
        'Division' => '({dropdown_4} + 100 - 5000) / 1000',
        'Ceil function' => 'ceil(({dropdown_4} + 100 - 5000) / 1000)',
        'Max function' => 'max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))',
        'Multiplication' => '0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))',
        'Addition again' => '1 + 0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))',
        'Final formula' => '5000000 * (1 + 0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000)))'
    );
    
    foreach ($tests as $description => $formula) {
        echo "<h3>$description:</h3>";
        $engine->evaluate($formula);
    }
    
    echo "<h2>5. Manual Verification:</h2>";
    $dropdown_value = $db_variables['dropdown_4'] ?? 6000;
    echo "Using dropdown_4 = $dropdown_value<br>";
    echo "Step 1: $dropdown_value + 100 - 5000 = " . ($dropdown_value + 100 - 5000) . "<br>";
    echo "Step 2: " . ($dropdown_value + 100 - 5000) . " / 1000 = " . (($dropdown_value + 100 - 5000) / 1000) . "<br>";
    echo "Step 3: ceil(" . (($dropdown_value + 100 - 5000) / 1000) . ") = " . ceil(($dropdown_value + 100 - 5000) / 1000) . "<br>";
    echo "Step 4: max(0, " . ceil(($dropdown_value + 100 - 5000) / 1000) . ") = " . max(0, ceil(($dropdown_value + 100 - 5000) / 1000)) . "<br>";
    echo "Step 5: 0.2 * " . max(0, ceil(($dropdown_value + 100 - 5000) / 1000)) . " = " . (0.2 * max(0, ceil(($dropdown_value + 100 - 5000) / 1000))) . "<br>";
    echo "Step 6: 1 + " . (0.2 * max(0, ceil(($dropdown_value + 100 - 5000) / 1000))) . " = " . (1 + 0.2 * max(0, ceil(($dropdown_value + 100 - 5000) / 1000))) . "<br>";
    echo "Step 7: 5000000 * " . (1 + 0.2 * max(0, ceil(($dropdown_value + 100 - 5000) / 1000))) . " = " . (5000000 * (1 + 0.2 * max(0, ceil(($dropdown_value + 100 - 5000) / 1000)))) . "<br>";
    
    echo "<h2 style='color: green;'>Expected Result: " . number_format(5000000 * (1 + 0.2 * max(0, ceil(($dropdown_value + 100 - 5000) / 1000)))) . "</h2>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
