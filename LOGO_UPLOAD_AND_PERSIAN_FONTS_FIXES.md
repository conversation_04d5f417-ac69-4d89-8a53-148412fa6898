# CFB Calculator - Logo Upload & Persian Fonts Fixes

## 🔧 **Logo Upload Issues Fixed:**

### **Problem:**
- Logo upload functionality was not working
- WordPress Media Library not properly enqueued
- JavaScript errors preventing media uploader

### **Solutions Implemented:**

#### 1. **WordPress Media Library Integration**
```php
// Added to render_settings_page()
wp_enqueue_media();
```

#### 2. **Enhanced JavaScript Error Handling**
```javascript
// Check if wp.media is available
if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
    alert('Media uploader not available. Please refresh the page.');
    return;
}
```

#### 3. **Improved Event Handling**
```javascript
// Use delegated event handling
$(document).on('click', '.cfb-upload-logo-btn', function(e) {
    // Event handler code
});
```

#### 4. **Better Form Processing**
```php
// Enhanced logo URL handling
elseif ($field === 'cfb_company_logo') {
    update_option($field, sanitize_url($value));
}
```

#### 5. **Debug Logging**
```javascript
console.log('Selected attachment:', attachment);
$('#cfb_company_logo').trigger('change');
```

## 🔤 **Persian Fonts Integration:**

### **Fonts Added:**
1. **XYekan** - Modern Persian font
2. **XNazanin** - Traditional Persian calligraphy
3. **XZar** - Clean, readable Persian font

### **Features Implemented:**

#### 1. **Font Selection in Settings**
```php
<optgroup label="Persian Fonts">
    <option value="xyekan">XYekan (Persian)</option>
    <option value="xnazanin">XNazanin (Persian)</option>
    <option value="xzar">XZar (Persian)</option>
</optgroup>
```

#### 2. **Font Availability Detection**
```php
private function is_persian_font_available($font_key) {
    if (!defined('K_PATH_FONTS')) {
        return false;
    }
    $font_path = K_PATH_FONTS . $font_key . '.php';
    return file_exists($font_path);
}
```

#### 3. **Smart Font Loading in PDF Generator**
```php
private function set_pdf_font($pdf, $font_family, $font_size, $rtl_support = false, $style = '') {
    $persian_fonts = ['xyekan', 'xnazanin', 'xzar'];
    
    try {
        if (in_array($font_family, $persian_fonts)) {
            $font_path = K_PATH_FONTS . $font_family . '.php';
            if (file_exists($font_path)) {
                $pdf->SetFont($font_family, $style, $font_size);
            } else {
                // Fallback to DejaVu Sans
                $pdf->SetFont('dejavusans', $style, $font_size);
            }
        }
        // ... more font handling
    } catch (Exception $e) {
        // Fallback to Helvetica
        $pdf->SetFont('helvetica', $style, $font_size);
    }
}
```

#### 4. **Font Status Indicators**
- ✅ Green indicator when Persian fonts are installed
- ⚠️ Warning when fonts are missing
- Direct link to font installer

#### 5. **Disabled Options for Missing Fonts**
```php
$font_available = $this->is_persian_font_available($font_key);
$font_label = $font_available ? $font_name : $font_name . ' (Not Installed)';
$disabled = $font_available ? '' : 'disabled';
```

## 🛠️ **Helper Tools Created:**

### 1. **Persian Font Tester** (`test-persian-fonts.php`)
- Tests font availability
- Generates sample PDF with Persian text
- Shows TCPDF configuration
- Lists all available fonts

### 2. **Font Installer** (`install-persian-fonts.php`)
- Automatic font installation from TTF files
- Manual installation instructions
- Directory permission checks
- Font conversion utilities

### 3. **Font Status Dashboard**
- Real-time font availability checking
- Installation progress tracking
- Error reporting and troubleshooting

## 📁 **Files Modified:**

### **Settings Page** (`includes/class-cfb-settings.php`)
- ✅ Added `wp_enqueue_media()` for logo upload
- ✅ Enhanced logo upload JavaScript
- ✅ Added Persian font options
- ✅ Added font availability checking
- ✅ Added font status indicators
- ✅ Improved form validation

### **PDF Generator** (`includes/class-cfb-pdf-generator.php`)
- ✅ Added `set_pdf_font()` helper method
- ✅ Added Persian font support
- ✅ Enhanced error handling
- ✅ Added font fallback system
- ✅ Updated all font calls to use new method

## 🎯 **Installation Instructions:**

### **For Logo Upload:**
1. ✅ **Already Fixed** - No additional steps needed
2. Logo upload should work immediately
3. Test by going to Settings → PDF & Invoices → Company Logo

### **For Persian Fonts:**

#### **Step 1: Get Font Files**
- Download XYekan, XNazanin, XZar TTF files
- Ensure proper licensing for commercial use

#### **Step 2: Install Fonts**
```bash
# Option A: Use the installer script
http://your-site.com/wp-content/plugins/CFB/install-persian-fonts.php

# Option B: Manual installation
1. Copy TTF files to: /wp-content/plugins/CFB/fonts/
2. Run the installer script
3. Or manually convert using TCPDF tools
```

#### **Step 3: Verify Installation**
```bash
# Test fonts
http://your-site.com/wp-content/plugins/CFB/test-persian-fonts.php
```

## 🧪 **Testing Checklist:**

### **Logo Upload Testing:**
- [ ] Click "Upload Logo" button
- [ ] WordPress Media Library opens
- [ ] Select image file
- [ ] Preview shows selected image
- [ ] "Remove Logo" button appears
- [ ] Form saves logo URL correctly

### **Persian Font Testing:**
- [ ] Persian fonts appear in dropdown
- [ ] Missing fonts show "(Not Installed)"
- [ ] Font status indicator shows correct count
- [ ] PDF generation uses selected Persian font
- [ ] Fallback works when font missing
- [ ] RTL text displays correctly

## 🔍 **Troubleshooting:**

### **Logo Upload Issues:**
```javascript
// Check browser console for errors
// Verify wp.media is loaded
console.log(typeof wp.media);

// Check if media scripts are enqueued
// Look for 'media-upload' and 'media-views' scripts
```

### **Persian Font Issues:**
```php
// Check TCPDF fonts directory
echo K_PATH_FONTS;

// Verify font files exist
$font_path = K_PATH_FONTS . 'xyekan.php';
echo file_exists($font_path) ? 'Exists' : 'Missing';

// Check directory permissions
echo is_writable(K_PATH_FONTS) ? 'Writable' : 'Not writable';
```

## 📋 **Next Steps:**

1. **Test logo upload functionality**
2. **Install Persian font TTF files**
3. **Run font installer script**
4. **Test PDF generation with Persian fonts**
5. **Verify RTL text rendering**
6. **Test fallback mechanisms**

## 🎉 **Expected Results:**

### **Logo Upload:**
- ✅ Smooth WordPress Media Library integration
- ✅ Live preview of selected logo
- ✅ Easy logo management (upload/remove)
- ✅ Logo appears in generated PDFs

### **Persian Fonts:**
- ✅ Beautiful Persian text in PDFs
- ✅ Proper RTL text direction
- ✅ Font fallback for missing fonts
- ✅ Clear status indicators in settings
- ✅ Professional Persian typography

The enhanced system now provides **professional logo management** and **comprehensive Persian font support** with robust error handling and user-friendly interfaces!
