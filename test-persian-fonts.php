<?php
/**
 * Test Persian Fonts for CFB Calculator
 * Check if Persian fonts are properly installed in TCPDF
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

// Load TCPDF
require_once(plugin_dir_path(__FILE__) . 'vendor/tecnickcom/tcpdf/tcpdf.php');

echo "<h1>CFB Calculator - Persian Fonts Test</h1>";

// Check if TCPDF is available
if (!class_exists('TCPDF')) {
    echo "<p style='color: red;'>❌ TCPDF library not found!</p>";
    exit;
}

echo "<p style='color: green;'>✅ TCPDF library loaded successfully</p>";

// Persian fonts to check
$persian_fonts = [
    'xyekan' => 'XYekan',
    'xnazanin' => 'XNazanin', 
    'xzar' => 'XZar'
];

echo "<h2>Persian Font Availability Check:</h2>";

foreach ($persian_fonts as $font_key => $font_name) {
    $font_path = K_PATH_FONTS . $font_key . '.php';
    $font_exists = file_exists($font_path);
    
    $status = $font_exists ? '✅' : '❌';
    $color = $font_exists ? 'green' : 'red';
    
    echo "<p style='color: $color;'>$status <strong>$font_name</strong> ($font_key)</p>";
    echo "<p style='margin-left: 20px; font-size: 12px; color: #666;'>Path: $font_path</p>";
    
    if ($font_exists) {
        // Try to get font info
        try {
            include($font_path);
            if (isset($desc)) {
                echo "<p style='margin-left: 20px; font-size: 12px; color: #333;'>Font loaded successfully</p>";
            }
        } catch (Exception $e) {
            echo "<p style='margin-left: 20px; font-size: 12px; color: red;'>Error loading font: " . $e->getMessage() . "</p>";
        }
    }
    echo "<br>";
}

echo "<h2>TCPDF Font Directory Info:</h2>";
echo "<p><strong>TCPDF Fonts Path:</strong> " . K_PATH_FONTS . "</p>";
echo "<p><strong>Directory Exists:</strong> " . (is_dir(K_PATH_FONTS) ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Directory Writable:</strong> " . (is_writable(K_PATH_FONTS) ? '✅ Yes' : '❌ No') . "</p>";

// List all available fonts in TCPDF
echo "<h2>All Available TCPDF Fonts:</h2>";
if (is_dir(K_PATH_FONTS)) {
    $font_files = glob(K_PATH_FONTS . '*.php');
    echo "<div style='max-height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;'>";
    foreach ($font_files as $font_file) {
        $font_name = basename($font_file, '.php');
        echo "<p style='margin: 2px 0;'>• $font_name</p>";
    }
    echo "</div>";
} else {
    echo "<p style='color: red;'>❌ TCPDF fonts directory not found!</p>";
}

// Test PDF generation with Persian fonts
echo "<h2>Test PDF Generation:</h2>";

try {
    $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
    $pdf->SetCreator('CFB Calculator');
    $pdf->SetTitle('Persian Font Test');
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);
    $pdf->SetMargins(20, 20, 20);
    $pdf->AddPage();
    
    echo "<p style='color: green;'>✅ PDF object created successfully</p>";
    
    // Test each Persian font
    $test_text_persian = 'سلام دنیا - تست فونت فارسی';
    $test_text_english = 'Hello World - Persian Font Test';
    
    $y_position = 30;
    
    foreach ($persian_fonts as $font_key => $font_name) {
        $font_path = K_PATH_FONTS . $font_key . '.php';
        
        if (file_exists($font_path)) {
            try {
                $pdf->SetFont($font_key, '', 14);
                $pdf->SetXY(20, $y_position);
                $pdf->Cell(0, 10, "$font_name: $test_text_persian", 0, 1, 'R');
                $y_position += 15;
                
                $pdf->SetXY(20, $y_position);
                $pdf->Cell(0, 10, "$font_name: $test_text_english", 0, 1, 'L');
                $y_position += 20;
                
                echo "<p style='color: green;'>✅ $font_name font test successful</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ $font_name font test failed: " . $e->getMessage() . "</p>";
                
                // Fallback to DejaVu Sans
                try {
                    $pdf->SetFont('dejavusans', '', 14);
                    $pdf->SetXY(20, $y_position);
                    $pdf->Cell(0, 10, "Fallback (DejaVu): $test_text_persian", 0, 1, 'R');
                    $y_position += 20;
                } catch (Exception $e2) {
                    echo "<p style='color: red;'>❌ Fallback font also failed: " . $e2->getMessage() . "</p>";
                }
            }
        }
    }
    
    // Save test PDF
    $upload_dir = wp_upload_dir();
    $pdf_dir = $upload_dir['basedir'] . '/cfb-test/';
    
    if (!file_exists($pdf_dir)) {
        wp_mkdir_p($pdf_dir);
    }
    
    $pdf_file = $pdf_dir . 'persian-font-test.pdf';
    $pdf->Output($pdf_file, 'F');
    
    $pdf_url = $upload_dir['baseurl'] . '/cfb-test/persian-font-test.pdf';
    echo "<p style='color: green;'>✅ Test PDF generated successfully!</p>";
    echo "<p><a href='$pdf_url' target='_blank' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>📄 Download Test PDF</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ PDF generation failed: " . $e->getMessage() . "</p>";
}

echo "<h2>Font Installation Instructions:</h2>";
echo "<div style='background: #f9f9f9; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<p><strong>To install Persian fonts in TCPDF:</strong></p>";
echo "<ol>";
echo "<li>Copy your Persian font files (.ttf) to the TCPDF fonts directory</li>";
echo "<li>Use TCPDF's font converter to generate .php font files</li>";
echo "<li>Or use the TCPDF font utility: <code>tcpdf_addfont.php</code></li>";
echo "</ol>";
echo "<p><strong>TCPDF Fonts Directory:</strong> <code>" . K_PATH_FONTS . "</code></p>";
echo "<p><strong>Expected files for each font:</strong></p>";
echo "<ul>";
echo "<li><code>xyekan.php</code> - Font definition file</li>";
echo "<li><code>xyekan.z</code> - Compressed font data</li>";
echo "<li><code>xyekan.ctg.z</code> - Character table (if needed)</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Current CFB Settings:</h2>";
$current_font = get_option('cfb_pdf_font_family', 'helvetica');
$rtl_support = get_option('cfb_pdf_rtl_support', 0);

echo "<p><strong>Current PDF Font:</strong> $current_font</p>";
echo "<p><strong>RTL Support:</strong> " . ($rtl_support ? 'Enabled' : 'Disabled') . "</p>";

echo "<h2>Recommendations:</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 4px;'>";
echo "<p><strong>For best Persian text support:</strong></p>";
echo "<ul>";
echo "<li>✅ Enable RTL support in CFB settings</li>";
echo "<li>✅ Install Persian fonts (XYekan, XNazanin, XZar) in TCPDF</li>";
echo "<li>✅ Use DejaVu Sans as fallback for Unicode support</li>";
echo "<li>✅ Test PDF generation with Persian text</li>";
echo "</ul>";
echo "</div>";
?>
