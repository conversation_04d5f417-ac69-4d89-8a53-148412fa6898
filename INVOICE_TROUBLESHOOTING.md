# CFB Calculator Invoice System Troubleshooting

## Issue: "Network error occurred while creating invoice"

### Root Causes Identified and Fixed:

#### 1. **Database Tables Missing**
**Problem**: The new invoice tables (`cfb_invoices` and `cfb_invoice_items`) were not being created properly.

**Solution**: 
- Added `force_create_invoice_tables()` method to main plugin file
- Enhanced activation hook to ensure tables are created
- Added manual table creation script (`fix-invoice-tables.php`)

#### 2. **Nonce Mismatch**
**Problem**: Frontend was using `cfb_calculator_nonce` but backend expected `cfb_invoice_nonce`.

**Solution**: 
- Updated AJAX handlers to use `cfb_calculator_nonce` for consistency
- Modified both `CFB_Invoices::ajax_save_invoice()` and `CFB_PDF_Generator::ajax_generate_pdf()`

#### 3. **Permission Issues**
**Problem**: Invoice creation required `manage_options` capability, but frontend users don't have this.

**Solution**: 
- Removed capability check for frontend invoice creation
- Allow both admin and frontend users to create invoices

#### 4. **Missing Error Handling**
**Problem**: Database errors were not being properly caught and reported.

**Solution**: 
- Added comprehensive error logging to invoice creation
- Enhanced database error handling with specific error messages
- Added validation for required fields

#### 5. **Script Enqueuing Issues**
**Problem**: Frontend scripts might not be loaded when shortcode is used.

**Solution**: 
- Modified shortcode handler to ensure scripts are enqueued
- Added debugging console logs to frontend JavaScript

### Files Modified:

1. **`cfb-calculator.php`**:
   - Enhanced activation hook
   - Added `force_create_invoice_tables()` method
   - Fixed shortcode script enqueuing

2. **`includes/class-cfb-invoices.php`**:
   - Fixed nonce verification
   - Removed capability check for frontend users
   - Added comprehensive error logging
   - Enhanced data validation

3. **`includes/class-cfb-pdf-generator.php`**:
   - Fixed nonce verification
   - Improved error handling

4. **`assets/js/frontend.js`**:
   - Added detailed console logging for debugging
   - Enhanced error reporting

### Troubleshooting Steps:

#### Step 1: Check Database Tables
Run the table creation script:
```
http://your-site.com/wp-content/plugins/CFB/fix-invoice-tables.php
```

#### Step 2: Test AJAX Endpoints
Use the test form in the fix script to verify AJAX functionality.

#### Step 3: Check Browser Console
Look for detailed error logs in the browser console when creating invoices.

#### Step 4: Check WordPress Error Logs
Look for CFB-specific error messages in your WordPress error logs.

#### Step 5: Verify Plugin Activation
Deactivate and reactivate the plugin to trigger table creation.

### Expected Behavior:

1. **After Calculation**: Invoice checkbox should appear when total > 0
2. **Form Validation**: Name and email are required fields
3. **AJAX Success**: Invoice should be created and PDF generation should start
4. **PDF Download**: PDF should open in new tab automatically
5. **Form Reset**: Invoice form should reset after successful creation

### Debug Information to Check:

1. **Database Tables**: Verify `wp_cfb_invoices` and `wp_cfb_invoice_items` exist
2. **AJAX Actions**: Confirm `cfb_save_invoice` and `cfb_generate_pdf` are registered
3. **Nonce Values**: Ensure nonce is properly generated and passed
4. **Console Logs**: Check for detailed error information in browser console
5. **WordPress Logs**: Look for CFB error messages in WordPress error logs

### Common Error Messages and Solutions:

#### "Security check failed"
- **Cause**: Nonce verification failed
- **Solution**: Clear cache, check nonce generation

#### "Customer name and email are required"
- **Cause**: Form validation failed
- **Solution**: Ensure form fields are properly filled

#### "Database error"
- **Cause**: Database table missing or SQL error
- **Solution**: Run table creation script, check database permissions

#### "Network error occurred"
- **Cause**: AJAX request failed
- **Solution**: Check AJAX URL, verify plugin activation, check server logs

### Testing Checklist:

- [ ] Database tables exist
- [ ] AJAX actions are registered
- [ ] Frontend scripts are loaded
- [ ] Nonce is properly generated
- [ ] Form validation works
- [ ] Invoice creation succeeds
- [ ] PDF generation works
- [ ] Error handling is functional

### Manual Testing Commands:

```php
// Check if tables exist
global $wpdb;
$invoices_table = $wpdb->prefix . 'cfb_invoices';
$exists = $wpdb->get_var("SHOW TABLES LIKE '$invoices_table'");
echo $exists ? 'Table exists' : 'Table missing';

// Test invoice creation
$result = $wpdb->insert($invoices_table, array(
    'invoice_number' => 'TEST-001',
    'form_id' => 1,
    'customer_name' => 'Test',
    'customer_email' => '<EMAIL>',
    'subtotal' => 100,
    'total_amount' => 100,
    'currency' => 'USD',
    'status' => 'draft'
));
echo $result ? 'Insert successful' : 'Insert failed: ' . $wpdb->last_error;
```

### Support Files Created:

1. **`fix-invoice-tables.php`**: Manual table creation and testing
2. **`test-invoice-tables.php`**: Comprehensive system testing
3. **`INVOICE_TROUBLESHOOTING.md`**: This troubleshooting guide

### Next Steps:

1. Run the fix script to create tables
2. Test invoice creation using the test form
3. Check browser console for any remaining errors
4. Verify PDF generation works
5. Test on frontend with actual calculator forms

The invoice system should now work properly with comprehensive error handling and debugging capabilities.
