<?php
/**
 * CFB Calculator System Check
 * Quick diagnostic to identify issues
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 CFB Calculator System Check</h1>";

// Test 1: Check if plugin is active
echo "<h2>1. Plugin Status</h2>";
if (is_plugin_active('cfb-calculator/cfb-calculator.php')) {
    echo "✅ Plugin is active<br>";
} else {
    echo "❌ Plugin is not active<br>";
}

// Test 2: Check if classes exist
echo "<h2>2. Class Existence</h2>";
$classes = [
    'CFB_Calculator',
    'CFB_Database', 
    'CFB_Form_Builder',
    'CFB_Formula_Engine',
    'CFB_Frontend',
    'CFB_Admin',
    'CFB_Settings',
    'CFB_Variables'
];

foreach ($classes as $class) {
    if (class_exists($class)) {
        echo "✅ {$class} exists<br>";
    } else {
        echo "❌ {$class} missing<br>";
    }
}

// Test 3: Check database tables
echo "<h2>3. Database Tables</h2>";
global $wpdb;
$tables = [
    'cfb_forms' => $wpdb->prefix . 'cfb_forms',
    'cfb_form_fields' => $wpdb->prefix . 'cfb_form_fields', 
    'cfb_form_submissions' => $wpdb->prefix . 'cfb_form_submissions',
    'cfb_variables' => $wpdb->prefix . 'cfb_variables'
];

foreach ($tables as $name => $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'");
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo "✅ {$name} exists ({$count} records)<br>";
    } else {
        echo "❌ {$name} missing<br>";
    }
}

// Test 4: Check WordPress hooks
echo "<h2>4. WordPress Hooks</h2>";
$hooks = [
    'admin_menu' => 'CFB_Calculator',
    'admin_enqueue_scripts' => 'CFB_Calculator',
    'wp_ajax_cfb_save_form' => 'CFB_Calculator',
    'wp_ajax_cfb_calculate_price' => 'CFB_Calculator',
    'wp_ajax_cfb_save_variable' => 'CFB_Variables',
    'wp_ajax_cfb_save_settings' => 'CFB_Settings'
];

foreach ($hooks as $hook => $class) {
    if (has_action($hook)) {
        echo "✅ {$hook} registered<br>";
    } else {
        echo "❌ {$hook} missing<br>";
    }
}

// Test 5: Check settings
echo "<h2>5. Plugin Settings</h2>";
$settings = [
    'cfb_currency_symbol',
    'cfb_currency_position',
    'cfb_decimal_places'
];

foreach ($settings as $setting) {
    $value = get_option($setting);
    if ($value !== false) {
        echo "✅ {$setting} = {$value}<br>";
    } else {
        echo "❌ {$setting} not set<br>";
    }
}

// Test 6: Check file permissions
echo "<h2>6. File Permissions</h2>";
$files = [
    'cfb-calculator.php',
    'includes/class-cfb-database.php',
    'includes/class-cfb-variables.php',
    'includes/class-cfb-settings.php',
    'assets/js/variables.js',
    'assets/css/admin.css'
];

$plugin_dir = WP_PLUGIN_DIR . '/cfb-calculator/';
foreach ($files as $file) {
    $path = $plugin_dir . $file;
    if (file_exists($path)) {
        if (is_readable($path)) {
            echo "✅ {$file} readable<br>";
        } else {
            echo "❌ {$file} not readable<br>";
        }
    } else {
        echo "❌ {$file} missing<br>";
    }
}

// Test 7: Test AJAX endpoints
echo "<h2>7. AJAX Endpoints Test</h2>";
$ajax_actions = [
    'cfb_save_variable',
    'cfb_delete_variable', 
    'cfb_save_settings',
    'cfb_calculate_price'
];

foreach ($ajax_actions as $action) {
    $url = admin_url('admin-ajax.php?action=' . $action);
    echo "📍 {$action}: <a href='{$url}' target='_blank'>{$url}</a><br>";
}

// Test 8: JavaScript/CSS enqueue test
echo "<h2>8. Asset Enqueue Test</h2>";
if (is_admin()) {
    echo "✅ In admin area - assets should be enqueued<br>";
    
    // Check if scripts are registered
    global $wp_scripts, $wp_styles;
    
    $scripts = ['cfb-calculator-admin', 'cfb-variables-js'];
    foreach ($scripts as $script) {
        if (isset($wp_scripts->registered[$script])) {
            echo "✅ Script {$script} registered<br>";
        } else {
            echo "❌ Script {$script} not registered<br>";
        }
    }
    
    $styles = ['cfb-calculator-admin'];
    foreach ($styles as $style) {
        if (isset($wp_styles->registered[$style])) {
            echo "✅ Style {$style} registered<br>";
        } else {
            echo "❌ Style {$style} not registered<br>";
        }
    }
} else {
    echo "ℹ️ Not in admin area<br>";
}

// Test 9: Create test variable
echo "<h2>9. Test Variable Creation</h2>";
try {
    if (class_exists('CFB_Database')) {
        $db = CFB_Database::get_instance();
        
        // Try to create a test variable
        $test_variable = array(
            'name' => 'test_var',
            'label' => 'Test Variable',
            'value' => 10.50,
            'description' => 'Test variable for system check',
            'category' => 'general',
            'icon' => 'dashicons-admin-settings',
            'color' => '#667eea',
            'is_active' => 1
        );
        
        $variable_id = $db->save_variable($test_variable);
        
        if ($variable_id) {
            echo "✅ Test variable created (ID: {$variable_id})<br>";
            
            // Clean up - delete test variable
            $db->delete_variable($variable_id);
            echo "✅ Test variable cleaned up<br>";
        } else {
            echo "❌ Failed to create test variable<br>";
        }
    } else {
        echo "❌ CFB_Database class not available<br>";
    }
} catch (Exception $e) {
    echo "❌ Error creating test variable: " . $e->getMessage() . "<br>";
}

// Test 10: Memory and PHP info
echo "<h2>10. System Info</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "WordPress Version: " . get_bloginfo('version') . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";

// Test 11: Check for common issues
echo "<h2>11. Common Issues Check</h2>";

// Check if tables need to be created
$forms_table = $wpdb->prefix . 'cfb_forms';
if (!$wpdb->get_var("SHOW TABLES LIKE '$forms_table'")) {
    echo "⚠️ Database tables missing - try deactivating and reactivating the plugin<br>";
}

// Check if variables table exists
$variables_table = $wpdb->prefix . 'cfb_variables';
if (!$wpdb->get_var("SHOW TABLES LIKE '$variables_table'")) {
    echo "⚠️ Variables table missing - creating now...<br>";
    try {
        CFB_Database::get_instance()->create_tables();
        echo "✅ Tables created successfully<br>";
    } catch (Exception $e) {
        echo "❌ Failed to create tables: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>✅ System Check Complete</h2>";
echo "<p>If you see any ❌ errors above, those need to be fixed for the plugin to work properly.</p>";
echo "<p>If you see ⚠️ warnings, those should be addressed but may not prevent basic functionality.</p>";

// Quick fix buttons
echo "<h3>Quick Fixes</h3>";
echo "<a href='?fix=tables' class='button'>Create Missing Tables</a> ";
echo "<a href='?fix=settings' class='button'>Reset Settings</a> ";
echo "<a href='?fix=clear_cache' class='button'>Clear Cache</a>";

// Handle quick fixes
if (isset($_GET['fix'])) {
    echo "<hr>";
    switch ($_GET['fix']) {
        case 'tables':
            try {
                CFB_Database::get_instance()->create_tables();
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;'>✅ Tables created successfully!</div>";
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24;'>❌ Error: " . $e->getMessage() . "</div>";
            }
            break;
        case 'settings':
            $default_settings = array(
                'cfb_currency_symbol' => '$',
                'cfb_currency_position' => 'left',
                'cfb_decimal_places' => 2,
                'cfb_thousand_separator' => ',',
                'cfb_decimal_separator' => '.'
            );
            foreach ($default_settings as $key => $value) {
                update_option($key, $value);
            }
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;'>✅ Settings reset to defaults!</div>";
            break;
        case 'clear_cache':
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
            }
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 4px; color: #155724;'>✅ Cache cleared!</div>";
            break;
    }
}
?>
