# CFB Calculator Language Template
# Copyright (C) 2024 CFB Team
# This file is distributed under the same license as the CFB Calculator package.
msgid ""
msgstr ""
"Project-Id-Version: CFB Calculator 1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 00:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: cfb-calculator.php:146
msgid "CFB Calculator"
msgstr ""

#: cfb-calculator.php:157
msgid "All Forms"
msgstr ""

#: cfb-calculator.php:167
msgid "Add New Form"
msgstr ""

#: cfb-calculator.php:175
msgid "Settings"
msgstr ""

#: cfb-calculator.php:210
msgid "Unauthorized access"
msgstr ""

#: includes/class-cfb-admin.php:45
msgid "CFB Calculator Forms"
msgstr ""

#: includes/class-cfb-admin.php:46
msgid "Add New"
msgstr ""

#: includes/class-cfb-admin.php:53
msgid "No forms found"
msgstr ""

#: includes/class-cfb-admin.php:54
msgid "Create your first price calculation form to get started."
msgstr ""

#: includes/class-cfb-admin.php:56
msgid "Create Your First Form"
msgstr ""

#: includes/class-cfb-admin.php:125
msgid "Active"
msgstr ""

#: includes/class-cfb-admin.php:125
msgid "Inactive"
msgstr ""

#: includes/class-cfb-admin.php:129
msgid "%d fields"
msgstr ""

#: includes/class-cfb-admin.php:130
msgid "%d submissions"
msgstr ""

#: includes/class-cfb-admin.php:134
msgid "Edit"
msgstr ""

#: includes/class-cfb-admin.php:137
msgid "Duplicate"
msgstr ""

#: includes/class-cfb-admin.php:140
msgid "Deactivate"
msgstr ""

#: includes/class-cfb-admin.php:140
msgid "Activate"
msgstr ""

#: includes/class-cfb-admin.php:143
msgid "Delete"
msgstr ""

#: includes/class-cfb-admin.php:146
msgid "Click to copy shortcode"
msgstr ""

#: includes/class-cfb-admin.php:152
msgid "Created: %s"
msgstr ""

#: includes/class-cfb-admin.php:155
msgid "Updated: %s"
msgstr ""

#: includes/class-cfb-form-builder.php:25
msgid "Text Field"
msgstr ""

#: includes/class-cfb-form-builder.php:29
msgid "Number Field"
msgstr ""

#: includes/class-cfb-form-builder.php:33
msgid "Slider"
msgstr ""

#: includes/class-cfb-form-builder.php:37
msgid "Dropdown"
msgstr ""

#: includes/class-cfb-form-builder.php:41
msgid "Radio Buttons"
msgstr ""

#: includes/class-cfb-form-builder.php:45
msgid "Checkboxes"
msgstr ""

#: includes/class-cfb-form-builder.php:85
msgid "Select an option"
msgstr ""

#: includes/class-cfb-form-builder.php:88
#: includes/class-cfb-form-builder.php:100
#: includes/class-cfb-form-builder.php:112
msgid "Option 1"
msgstr ""

#: includes/class-cfb-form-builder.php:89
#: includes/class-cfb-form-builder.php:101
#: includes/class-cfb-form-builder.php:113
msgid "Option 2"
msgstr ""

#: includes/class-cfb-form-builder.php:140
msgid "Form name is required"
msgstr ""

#: includes/class-cfb-form-builder.php:157
msgid "Form saved successfully"
msgstr ""

#: includes/class-cfb-form-builder.php:160
msgid "Failed to save form"
msgstr ""

#: includes/class-cfb-form-builder.php:175
msgid "Edit Form"
msgstr ""

#: includes/class-cfb-form-builder.php:175
msgid "Create New Form"
msgstr ""

#: includes/class-cfb-form-builder.php:180
msgid "Form Settings"
msgstr ""

#: includes/class-cfb-form-builder.php:183
msgid "Form Name"
msgstr ""

#: includes/class-cfb-form-builder.php:184
msgid "Enter form name"
msgstr ""

#: includes/class-cfb-form-builder.php:188
msgid "Description"
msgstr ""

#: includes/class-cfb-form-builder.php:189
msgid "Enter form description"
msgstr ""

#: includes/class-cfb-form-builder.php:193
msgid "Calculation Settings"
msgstr ""

#: includes/class-cfb-form-builder.php:197
msgid "Enable Subtotals"
msgstr ""

#: includes/class-cfb-form-builder.php:201
msgid "Save Submissions"
msgstr ""

#: includes/class-cfb-form-builder.php:207
msgid "Field Types"
msgstr ""

#: includes/class-cfb-form-builder.php:218
msgid "Form Fields"
msgstr ""

#: includes/class-cfb-form-builder.php:227
msgid "Drag field types here to build your form"
msgstr ""

#: includes/class-cfb-form-builder.php:232
msgid "Calculation Configuration"
msgstr ""

#: includes/class-cfb-form-builder.php:235
msgid "Subtotals"
msgstr ""

#: includes/class-cfb-form-builder.php:242
msgid "Add Subtotal"
msgstr ""

#: includes/class-cfb-form-builder.php:246
msgid "Total Formula"
msgstr ""

#: includes/class-cfb-form-builder.php:247
msgid "Enter total formula (e.g., subtotal_1 + subtotal_2)"
msgstr ""

#: includes/class-cfb-form-builder.php:248
msgid "Use field names in {brackets} and subtotal_1, subtotal_2, etc. for subtotals. Supports: +, -, *, /, ceil(), floor(), min(), max(), pow(), if()"
msgstr ""

#: includes/class-cfb-form-builder.php:254
msgid "Save Form"
msgstr ""

#: includes/class-cfb-form-builder.php:255
msgid "Preview"
msgstr ""

#: includes/class-cfb-form-builder.php:256
msgid "Back to Forms"
msgstr ""

#: includes/class-cfb-form-builder.php:264
msgid "Edit Field"
msgstr ""

#: includes/class-cfb-form-builder.php:267
msgid "Delete Field"
msgstr ""

#: includes/class-cfb-form-builder.php:284
msgid "Subtotal Label"
msgstr ""

#: includes/class-cfb-form-builder.php:285
msgid "Enter subtotal label"
msgstr ""

#: includes/class-cfb-form-builder.php:288
msgid "Formula"
msgstr ""

#: includes/class-cfb-form-builder.php:289
msgid "Enter formula"
msgstr ""

#: includes/class-cfb-form-builder.php:291
msgid "Remove"
msgstr ""

#: includes/class-cfb-frontend.php:25
msgid "Invalid form ID"
msgstr ""

#: includes/class-cfb-frontend.php:30
msgid "Form not found"
msgstr ""

#: includes/class-cfb-frontend.php:55
msgid "Breakdown"
msgstr ""

#: includes/class-cfb-frontend.php:66
msgid "Total"
msgstr ""

#: includes/class-cfb-frontend.php:73
msgid "Calculate"
msgstr ""

#: includes/class-cfb-frontend.php:74
msgid "Reset"
msgstr ""

#: includes/class-cfb-frontend.php:78
msgid "Calculating..."
msgstr ""

#: includes/class-cfb-formula-engine.php:50
msgid "Invalid form data"
msgstr ""

#: includes/class-cfb-formula-engine.php:56
msgid "Form not found"
msgstr ""

#: includes/class-cfb-settings.php:45
msgid "CFB Calculator Settings"
msgstr ""

#: includes/class-cfb-settings.php:50
msgid "Currency"
msgstr ""

#: includes/class-cfb-settings.php:54
msgid "Display"
msgstr ""

#: includes/class-cfb-settings.php:58
msgid "Language"
msgstr ""

#: includes/class-cfb-settings.php:62
msgid "Advanced"
msgstr ""

#: includes/class-cfb-settings.php:70
msgid "Currency Settings"
msgstr ""

#: includes/class-cfb-settings.php:74
msgid "Currency Symbol"
msgstr ""

#: includes/class-cfb-settings.php:80
msgid "The symbol to display for currency (e.g., $, €, £, ﷼)"
msgstr ""

#: includes/class-cfb-settings.php:85
msgid "Currency Position"
msgstr ""

#: includes/class-cfb-settings.php:89
msgid "Left ($100)"
msgstr ""

#: includes/class-cfb-settings.php:92
msgid "Right (100$)"
msgstr ""

#: includes/class-cfb-settings.php:95
msgid "Position of currency symbol relative to the amount"
msgstr ""

#: includes/class-cfb-settings.php:100
msgid "Decimal Places"
msgstr ""

#: includes/class-cfb-settings.php:108
msgid "Number of decimal places to display"
msgstr ""

#: includes/class-cfb-settings.php:113
msgid "Thousand Separator"
msgstr ""

#: includes/class-cfb-settings.php:119
msgid "Character used to separate thousands (e.g., comma, space)"
msgstr ""

#: includes/class-cfb-settings.php:124
msgid "Decimal Separator"
msgstr ""

#: includes/class-cfb-settings.php:130
msgid "Character used to separate decimal places"
msgstr ""

#: includes/class-cfb-settings.php:137
msgid "Display Settings"
msgstr ""

#: includes/class-cfb-settings.php:141
msgid "Default Theme"
msgstr ""

#: includes/class-cfb-settings.php:145
msgid "Modern"
msgstr ""

#: includes/class-cfb-settings.php:148
msgid "Classic"
msgstr ""

#: includes/class-cfb-settings.php:151
msgid "Minimal"
msgstr ""

#: includes/class-cfb-settings.php:154
msgid "Default theme for calculator forms"
msgstr ""

#: includes/class-cfb-settings.php:159
msgid "Enable Animations"
msgstr ""

#: includes/class-cfb-settings.php:166
msgid "Enable smooth animations and transitions"
msgstr ""

#: includes/class-cfb-settings.php:172
msgid "Auto Calculate"
msgstr ""

#: includes/class-cfb-settings.php:179
msgid "Calculate automatically when form values change"
msgstr ""

#: includes/class-cfb-settings.php:187
msgid "Language & RTL Settings"
msgstr ""

#: includes/class-cfb-settings.php:191
msgid "RTL Support"
msgstr ""

#: includes/class-cfb-settings.php:198
msgid "Enable Right-to-Left (RTL) language support"
msgstr ""

#: includes/class-cfb-settings.php:200
msgid "Automatically adjusts layout for RTL languages like Arabic, Hebrew, Farsi"
msgstr ""

#: includes/class-cfb-settings.php:205
msgid "Default Language"
msgstr ""

#: includes/class-cfb-settings.php:209
msgid "English"
msgstr ""

#: includes/class-cfb-settings.php:212
msgid "Farsi (Persian)"
msgstr ""

#: includes/class-cfb-settings.php:215
msgid "Arabic"
msgstr ""

#: includes/class-cfb-settings.php:218
msgid "Default language for new forms"
msgstr ""

#: includes/class-cfb-settings.php:226
msgid "Advanced Settings"
msgstr ""

#: includes/class-cfb-settings.php:230
msgid "Enable Caching"
msgstr ""

#: includes/class-cfb-settings.php:237
msgid "Cache calculation results for better performance"
msgstr ""

#: includes/class-cfb-settings.php:243
msgid "Debug Mode"
msgstr ""

#: includes/class-cfb-settings.php:250
msgid "Enable debug mode for troubleshooting"
msgstr ""

#: includes/class-cfb-settings.php:256
msgid "Custom CSS"
msgstr ""

#: includes/class-cfb-settings.php:263
msgid "Add custom CSS to style your calculator forms"
msgstr ""

#: includes/class-cfb-settings.php:268
msgid "Save Settings"
msgstr ""

#: includes/class-cfb-settings.php:269
msgid "Reset to Defaults"
msgstr ""

#: includes/class-cfb-settings.php:330
msgid "Settings saved successfully"
msgstr ""
