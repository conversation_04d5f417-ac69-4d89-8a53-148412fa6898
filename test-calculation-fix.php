<?php
/**
 * Comprehensive Formula and Variable Debug Test
 * Test to identify and fix formula calculation and variable recognition issues
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>CFB Calculator - Comprehensive Formula & Variable Debug Test</h1>";
echo "<p>Testing formula calculations and variable recognition in detail</p>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Test 1: Check database and variables
echo "<h2>1. Database and Variables Check</h2>";

global $wpdb;
$variables_table = $wpdb->prefix . 'cfb_variables';

// Check if variables table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$variables_table'");
if ($table_exists) {
    echo "✅ Variables table exists: $variables_table<br>";

    // Get all variables
    $variables = $wpdb->get_results("SELECT * FROM $variables_table");
    echo "Variables in database: " . count($variables) . "<br>";

    if (count($variables) > 0) {
        echo "<h3>Existing Variables:</h3>";
        foreach ($variables as $var) {
            echo "• {$var->name} = {$var->value} (Label: {$var->label}, Active: {$var->is_active})<br>";
        }
    } else {
        echo "<h3>No variables found. Creating test variables...</h3>";

        // Create test variables
        $test_variables = array(
            array('name' => 'price', 'label' => 'Price', 'value' => 10.50),
            array('name' => 'tax_rate', 'label' => 'Tax Rate', 'value' => 0.1),
            array('name' => 'discount', 'label' => 'Discount', 'value' => 5.00)
        );

        foreach ($test_variables as $var) {
            $result = $wpdb->insert(
                $variables_table,
                array(
                    'name' => $var['name'],
                    'label' => $var['label'],
                    'value' => $var['value'],
                    'is_active' => 1
                ),
                array('%s', '%s', '%f', '%d')
            );

            if ($result) {
                echo "✅ Created variable: {$var['name']} = {$var['value']}<br>";
            } else {
                echo "✗ Failed to create variable: {$var['name']}<br>";
            }
        }
    }
} else {
    echo "❌ Variables table missing. Creating...<br>";
    try {
        CFB_Database::get_instance()->create_tables();
        echo "✅ Tables created<br>";
    } catch (Exception $e) {
        echo "✗ Error creating tables: " . $e->getMessage() . "<br>";
    }
}

// Test 2: Test formula engine initialization and variable loading
echo "<h2>2. Formula Engine Variable Loading Test</h2>";

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "✅ Formula engine instance created<br>";

    // Test variable loading by calling load_global_variables method via reflection
    $reflection = new ReflectionClass($formula_engine);
    $method = $reflection->getMethod('load_global_variables');
    $method->setAccessible(true);
    $method->invoke($formula_engine);

    // Get variables property via reflection
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $loaded_variables = $property->getValue($formula_engine);

    echo "Variables loaded into formula engine: " . count($loaded_variables) . "<br>";
    if (count($loaded_variables) > 0) {
        echo "<h3>Loaded Variables:</h3>";
        foreach ($loaded_variables as $name => $value) {
            echo "• {$name} = {$value}<br>";
        }
    } else {
        echo "❌ No variables loaded into formula engine<br>";
    }

} catch (Exception $e) {
    echo "✗ Error testing formula engine: " . $e->getMessage() . "<br>";
}

// Test 3: Test variable replacement in formulas
echo "<h2>3. Variable Replacement in Formulas Test</h2>";

if (isset($formula_engine)) {
    // Test variable replacement
    $test_formulas = array(
        '{price}',
        '{price} * 2',
        '{price} + {discount}',
        '{price} * {tax_rate}',
        '({price} + {discount}) * {tax_rate}',
        '10 + {price}',
        '{price} * 1.1'
    );

    echo "<h3>Testing Variable Replacement:</h3>";
    foreach ($test_formulas as $formula) {
        try {
            echo "<strong>Formula:</strong> <code>$formula</code><br>";

            // Test variable replacement step by step
            $reflection = new ReflectionClass($formula_engine);
            $replace_method = $reflection->getMethod('replace_variables');
            $replace_method->setAccessible(true);

            $replaced_formula = $replace_method->invoke($formula_engine, $formula);
            echo "After variable replacement: <code>$replaced_formula</code><br>";

            $result = $formula_engine->evaluate_formula($formula);
            echo "Final result: <strong>$result</strong><br><br>";

        } catch (Exception $e) {
            echo "✗ Error with formula '$formula': " . $e->getMessage() . "<br><br>";
        }
    }
}

// Test 4: Test mathematical expressions
echo "<h2>4. Mathematical Expression Parsing Test</h2>";

if (isset($formula_engine)) {
    $math_expressions = array(
        '2 + 3',
        '10 - 4',
        '5 * 6',
        '20 / 4',
        '(2 + 3) * 4',
        '10 + (5 * 2)',
        '((2 + 3) * 4) - 5',
        '2 + 3 * 4',
        '(10 + 5) / 3'
    );

    echo "<h3>Testing Pure Mathematical Expressions:</h3>";
    foreach ($math_expressions as $expression) {
        try {
            $result = $formula_engine->evaluate_formula($expression);
            echo "✓ Expression: <code>$expression</code> = <strong>$result</strong><br>";
        } catch (Exception $e) {
            echo "✗ Error with expression '$expression': " . $e->getMessage() . "<br>";
        } catch (Error $e) {
            echo "✗ Fatal Error with expression '$expression': " . $e->getMessage() . "<br>";
        }
    }
}

// Test 5: Test form configuration with complex formulas
echo "<h2>5. Form Configuration Test</h2>";

$test_form_config = array(
    'fields' => array(
        array(
            'type' => 'number',
            'name' => 'quantity',
            'label' => 'Quantity',
            'conditional_logic' => array(
                'enabled' => false,
                'conditions' => array()
            )
        ),
        array(
            'type' => 'number',
            'name' => 'price',
            'label' => 'Price',
            'conditional_logic' => array(
                'enabled' => false,
                'conditions' => array()
            )
        ),
        array(
            'type' => 'calculation',
            'name' => 'subtotal',
            'label' => 'Subtotal',
            'formula' => '{quantity} * {price}',
            'display_type' => 'currency',
            'conditional_logic' => array(
                'enabled' => false,
                'conditions' => array()
            )
        ),
        array(
            'type' => 'calculation',
            'name' => 'tax',
            'label' => 'Tax (10%)',
            'formula' => '{subtotal} * 0.1',
            'display_type' => 'currency',
            'conditional_logic' => array(
                'enabled' => false,
                'conditions' => array()
            )
        ),
        array(
            'type' => 'calculation',
            'name' => 'total',
            'label' => 'Total with Complex Formula',
            'formula' => '({quantity} * {price}) + (({quantity} * {price}) * 0.1)',
            'display_type' => 'currency',
            'conditional_logic' => array(
                'enabled' => false,
                'conditions' => array()
            )
        )
    )
);

$test_form_data = array(
    'quantity' => 5,
    'price' => 10.50
);

$test_settings = array(
    'currency_symbol' => '$',
    'currency_position' => 'left',
    'decimal_places' => 2,
    'decimal_separator' => '.',
    'thousand_separator' => ','
);

echo "Form config created with " . count($test_form_config['fields']) . " fields<br>";
echo "Test form data: quantity=" . $test_form_data['quantity'] . ", price=" . $test_form_data['price'] . "<br>";

// Test the formula engine with complex formulas
echo "<h2>3. Testing Formula Engine with Complex Calculations</h2>";

try {
    if (!isset($formula_engine)) {
        $formula_engine = CFB_Formula_Engine::get_instance();
    }
    echo "Formula engine instance ready<br>";

    $result = $formula_engine->process_calculation($test_form_data, $test_form_config, $test_settings);
    echo "✓ Complex calculation completed successfully!<br>";
    echo "<h3>Calculation Results:</h3>";
    echo "<pre>" . print_r($result, true) . "</pre>";

    // Verify specific calculations
    if (isset($result['calculations'])) {
        echo "<h3>Individual Calculation Verification:</h3>";
        foreach ($result['calculations'] as $calc) {
            echo "• " . $calc['label'] . ": " . $calc['formatted'] . " (raw: " . $calc['value'] . ")<br>";
        }
    }

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
} catch (Error $e) {
    echo "✗ Fatal Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

// Test 4: Test with null/empty fields
echo "<h2>4. Testing with Null/Empty Fields</h2>";

$empty_form_config = array(
    'fields' => null
);

try {
    $result = $formula_engine->process_calculation(array(), $empty_form_config, $test_settings);
    echo "✓ Empty fields test passed<br>";
    echo "Result: " . print_r($result, true) . "<br>";
} catch (Exception $e) {
    echo "✗ Error with empty fields: " . $e->getMessage() . "<br>";
}

// Test 5: Test with missing fields array
echo "<h2>5. Testing with Missing Fields Array</h2>";

$missing_fields_config = array();

try {
    $result = $formula_engine->process_calculation(array(), $missing_fields_config, $test_settings);
    echo "✓ Missing fields test passed<br>";
    echo "Result: " . print_r($result, true) . "<br>";
} catch (Exception $e) {
    echo "✗ Error with missing fields: " . $e->getMessage() . "<br>";
}

echo "<h2>✅ Test Summary</h2>";
echo "<p><strong>All tests completed!</strong></p>";
echo "<p>If you see this message without any fatal errors about 'Argument #2 (\$index) cannot be passed by reference', then the parse_expression fix is working correctly.</p>";
echo "<p>The fix involved changing line 538 in class-cfb-formula-engine.php from:</p>";
echo "<code>\$result = \$this->parse_expression(\$tokens, 0);</code><br>";
echo "<p>To:</p>";
echo "<code>\$index = 0;<br>\$result = \$this->parse_expression(\$tokens, \$index);</code><br>";
echo "<p>This ensures that the \$index parameter is passed by reference as expected by the method signature.</p>";
?>
