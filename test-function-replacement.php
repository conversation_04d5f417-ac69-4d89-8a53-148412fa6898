<?php
/**
 * Test Function Replacement
 * Test if the function replacement is working correctly
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>Test Function Replacement</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "✅ Formula engine created<br>";
    
    // Set up test variables
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $property->setValue($formula_engine, array('dropdown_4' => 6000));
    
    echo "<h2>Testing Function Replacement Step by Step:</h2>";
    
    $test_formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))';
    echo "<p><strong>Original formula:</strong> <code>$test_formula</code></p>";
    
    // Step 1: Variable replacement
    $replace_vars_method = $reflection->getMethod('replace_variables');
    $replace_vars_method->setAccessible(true);
    $after_vars = $replace_vars_method->invoke($formula_engine, $test_formula);
    echo "<p><strong>After variables:</strong> <code>$after_vars</code></p>";
    
    // Step 2: Function replacement
    $replace_funcs_method = $reflection->getMethod('replace_functions');
    $replace_funcs_method->setAccessible(true);
    $after_funcs = $replace_funcs_method->invoke($formula_engine, $after_vars);
    echo "<p><strong>After functions:</strong> <code>$after_funcs</code></p>";
    
    // Step 3: Safe eval
    $safe_eval_method = $reflection->getMethod('safe_eval');
    $safe_eval_method->setAccessible(true);
    $result = $safe_eval_method->invoke($formula_engine, $after_funcs);
    echo "<p><strong>Final result:</strong> <span style='color: green; font-size: 20px;'>$result</span></p>";
    
    // Test individual functions
    echo "<h2>Testing Individual Functions:</h2>";
    
    $individual_tests = array(
        'ceil(1.1)' => 'Should be 2',
        'ceil(1.9)' => 'Should be 2', 
        'max(0, 2)' => 'Should be 2',
        'max(0, -1)' => 'Should be 0',
        'ceil((6000+100-5000)/1000)' => 'Should be 2',
        'max(0,ceil((6000+100-5000)/1000))' => 'Should be 2'
    );
    
    foreach ($individual_tests as $test => $expected) {
        echo "<h3>Testing: <code>$test</code></h3>";
        echo "<p>Expected: $expected</p>";
        
        try {
            $result = $formula_engine->evaluate_formula($test);
            echo "<p>Result: <strong style='color: green;'>$result</strong></p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
        echo "<hr>";
    }
    
    // Test the complete formula
    echo "<h2>Testing Complete Formula:</h2>";
    $complete_result = $formula_engine->evaluate_formula($test_formula);
    echo "<p><strong>Complete formula result:</strong> <span style='color: green; font-size: 24px;'>$complete_result</span></p>";
    
    // Manual calculation for verification
    echo "<h2>Manual Verification:</h2>";
    $dropdown_val = 6000;
    $step1 = $dropdown_val + 100 - 5000; // 1100
    $step2 = $step1 / 1000; // 1.1
    $step3 = ceil($step2); // 2
    $step4 = max(0, $step3); // 2
    $step5 = 0.2 * $step4; // 0.4
    $step6 = 1 + $step5; // 1.4
    $step7 = 5000000 * $step6; // 7000000
    
    echo "<p>Manual calculation:</p>";
    echo "<p>Step 1: $dropdown_val + 100 - 5000 = $step1</p>";
    echo "<p>Step 2: $step1 / 1000 = $step2</p>";
    echo "<p>Step 3: ceil($step2) = $step3</p>";
    echo "<p>Step 4: max(0, $step3) = $step4</p>";
    echo "<p>Step 5: 0.2 * $step4 = $step5</p>";
    echo "<p>Step 6: 1 + $step5 = $step6</p>";
    echo "<p>Step 7: 5000000 * $step6 = <strong style='color: blue; font-size: 20px;'>$step7</strong></p>";
    
    if ($complete_result == $step7) {
        echo "<p style='color: green; font-size: 18px;'>✅ <strong>SUCCESS!</strong> Formula calculation matches manual calculation!</p>";
    } else {
        echo "<p style='color: red; font-size: 18px;'>❌ <strong>MISMATCH!</strong> Formula: $complete_result, Manual: $step7</p>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<h2>Check Error Logs</h2>";
echo "<p>Check your error logs for detailed function replacement debugging information.</p>";
?>
