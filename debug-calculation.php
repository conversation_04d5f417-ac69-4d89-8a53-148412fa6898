<?php
/**
 * Debug Calculation Test
 * Add this to your WordPress root and access via: yoursite.com/debug-calculation.php
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>CFB Calculator Debug Test</h1>";

// Test 1: Check if classes exist
echo "<h2>1. Class Existence Check</h2>";
echo "CFB_Formula_Engine exists: " . (class_exists('CFB_Formula_Engine') ? 'YES' : 'NO') . "<br>";
echo "CFB_Database exists: " . (class_exists('CFB_Database') ? 'YES' : 'NO') . "<br>";

// Test 2: Check database tables
echo "<h2>2. Database Tables Check</h2>";
global $wpdb;
$forms_table = $wpdb->prefix . 'cfb_forms';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$forms_table'");
echo "Forms table exists: " . ($table_exists ? 'YES' : 'NO') . "<br>";

// Test 3: Check if we have any forms
echo "<h2>3. Forms Check</h2>";
if ($table_exists) {
    $forms = $wpdb->get_results("SELECT id, name FROM $forms_table");
    echo "Number of forms: " . count($forms) . "<br>";
    foreach ($forms as $form) {
        echo "Form ID: {$form->id}, Name: {$form->name}<br>";
    }
} else {
    echo "No forms table found<br>";
}

// Test 4: Simulate AJAX request
echo "<h2>4. AJAX Simulation Test</h2>";

if (count($forms) > 0) {
    $test_form_id = $forms[0]->id;
    echo "Testing with form ID: $test_form_id<br>";
    
    // Get form data
    $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM $forms_table WHERE id = %d", $test_form_id));
    if ($form) {
        echo "Form found: " . $form->name . "<br>";
        echo "Form data: " . substr($form->form_data, 0, 200) . "...<br>";
        
        // Try to decode form data
        $form_config = json_decode($form->form_data, true);
        if ($form_config) {
            echo "Form config decoded successfully<br>";
            echo "Number of fields: " . (isset($form_config['fields']) ? count($form_config['fields']) : 0) . "<br>";
            
            if (isset($form_config['fields'])) {
                foreach ($form_config['fields'] as $field) {
                    echo "Field: {$field['name']} (type: {$field['type']})<br>";
                }
            }
        } else {
            echo "Failed to decode form config<br>";
        }
        
        // Test calculation
        echo "<h3>Testing Calculation</h3>";
        try {
            // Simulate form data
            $test_form_data = array();
            if (isset($form_config['fields'])) {
                foreach ($form_config['fields'] as $field) {
                    switch ($field['type']) {
                        case 'number':
                        case 'slider':
                            $test_form_data[$field['name']] = 10;
                            break;
                        case 'dropdown':
                        case 'radio':
                            if (isset($field['options']) && !empty($field['options'])) {
                                $test_form_data[$field['name']] = $field['options'][0]['value'];
                            }
                            break;
                        case 'checkbox':
                            if (isset($field['options']) && !empty($field['options'])) {
                                $test_form_data[$field['name']] = array($field['options'][0]['value']);
                            }
                            break;
                        default:
                            $test_form_data[$field['name']] = 'test';
                    }
                }
            }
            
            echo "Test form data: " . print_r($test_form_data, true) . "<br>";
            
            // Test formula engine
            $formula_engine = CFB_Formula_Engine::get_instance();
            
            $settings = array(
                'currency_symbol' => '$',
                'currency_position' => 'left',
                'decimal_places' => 2,
                'decimal_separator' => '.',
                'thousand_separator' => ','
            );
            
            $result = $formula_engine->process_calculation($test_form_data, $form_config, $settings);
            echo "Calculation result: " . print_r($result, true) . "<br>";
            
        } catch (Exception $e) {
            echo "Calculation error: " . $e->getMessage() . "<br>";
            echo "Stack trace: " . $e->getTraceAsString() . "<br>";
        }
        
    } else {
        echo "Form not found<br>";
    }
} else {
    echo "No forms available for testing<br>";
}

// Test 5: Check WordPress AJAX
echo "<h2>5. WordPress AJAX Check</h2>";
echo "AJAX URL: " . admin_url('admin-ajax.php') . "<br>";
echo "Current user can manage options: " . (current_user_can('manage_options') ? 'YES' : 'NO') . "<br>";

// Test 6: Check nonce
echo "<h2>6. Nonce Check</h2>";
$nonce = wp_create_nonce('cfb_calculator_nonce');
echo "Generated nonce: $nonce<br>";
$verify = wp_verify_nonce($nonce, 'cfb_calculator_nonce');
echo "Nonce verification: " . ($verify ? 'PASS' : 'FAIL') . "<br>";

echo "<h2>Debug Complete</h2>";
echo "Check your error logs for detailed CFB debug information.";
?>
