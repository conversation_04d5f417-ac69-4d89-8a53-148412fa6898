<?php
/**
 * Check Database Contents
 * See what's actually in the CFB database tables
 */

// Database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'w1';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>CFB Database Contents</h1>";
    
    // Check what tables exist
    echo "<h2>1. Available Tables:</h2>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'wp_cfb_%'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "❌ No CFB tables found!<br>";
        echo "Looking for any tables with 'cfb' in the name...<br>";
        $stmt = $pdo->query("SHOW TABLES LIKE '%cfb%'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    foreach ($tables as $table) {
        echo "✅ Table: $table<br>";
    }
    
    // Check variables table
    echo "<h2>2. Variables Table:</h2>";
    try {
        $stmt = $pdo->query("SELECT * FROM wp_cfb_variables LIMIT 10");
        $variables = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($variables)) {
            echo "❌ No variables found in wp_cfb_variables<br>";
        } else {
            echo "✅ Found " . count($variables) . " variables:<br>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Label</th><th>Value</th><th>Active</th></tr>";
            foreach ($variables as $var) {
                echo "<tr>";
                echo "<td>" . ($var['id'] ?? 'N/A') . "</td>";
                echo "<td>" . ($var['name'] ?? 'N/A') . "</td>";
                echo "<td>" . ($var['label'] ?? 'N/A') . "</td>";
                echo "<td>" . ($var['value'] ?? 'N/A') . "</td>";
                echo "<td>" . ($var['is_active'] ?? 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "❌ Error accessing variables table: " . $e->getMessage() . "<br>";
    }
    
    // Check forms table
    echo "<h2>3. Forms Table:</h2>";
    try {
        $stmt = $pdo->query("SELECT id, name, description FROM wp_cfb_forms LIMIT 10");
        $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($forms)) {
            echo "❌ No forms found in wp_cfb_forms<br>";
        } else {
            echo "✅ Found " . count($forms) . " forms:<br>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Description</th></tr>";
            foreach ($forms as $form) {
                echo "<tr>";
                echo "<td>" . $form['id'] . "</td>";
                echo "<td>" . $form['name'] . "</td>";
                echo "<td>" . $form['description'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "❌ Error accessing forms table: " . $e->getMessage() . "<br>";
    }
    
    // Check for a specific form with dropdown_4
    echo "<h2>4. Looking for Forms with dropdown_4:</h2>";
    try {
        $stmt = $pdo->query("SELECT id, name, form_data FROM wp_cfb_forms WHERE form_data LIKE '%dropdown_4%'");
        $forms_with_dropdown = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($forms_with_dropdown)) {
            echo "❌ No forms found containing 'dropdown_4'<br>";
        } else {
            echo "✅ Found " . count($forms_with_dropdown) . " forms with dropdown_4:<br>";
            foreach ($forms_with_dropdown as $form) {
                echo "<h3>Form ID: {$form['id']} - {$form['name']}</h3>";
                $form_data = json_decode($form['form_data'], true);
                if ($form_data && isset($form_data['fields'])) {
                    echo "Fields in this form:<br>";
                    foreach ($form_data['fields'] as $field) {
                        if (isset($field['name'])) {
                            echo "• {$field['name']} ({$field['type'] ?? 'unknown type'})<br>";
                            if ($field['name'] === 'dropdown_4') {
                                echo "  <strong>Found dropdown_4!</strong><br>";
                                echo "  Field details: " . json_encode($field, JSON_PRETTY_PRINT) . "<br>";
                            }
                        }
                    }
                }
            }
        }
    } catch (Exception $e) {
        echo "❌ Error searching for dropdown_4: " . $e->getMessage() . "<br>";
    }
    
    // Create test variables if none exist
    echo "<h2>5. Creating Test Variables:</h2>";
    try {
        // Check if dropdown_4 variable exists
        $stmt = $pdo->prepare("SELECT * FROM wp_cfb_variables WHERE name = ?");
        $stmt->execute(['dropdown_4']);
        $existing = $stmt->fetch();
        
        if (!$existing) {
            $stmt = $pdo->prepare("INSERT INTO wp_cfb_variables (name, label, value, is_active) VALUES (?, ?, ?, ?)");
            $stmt->execute(['dropdown_4', 'Dropdown 4', '6000', 1]);
            echo "✅ Created dropdown_4 variable with value 6000<br>";
        } else {
            echo "✅ dropdown_4 variable already exists with value: " . $existing['value'] . "<br>";
        }
        
        // Create other test variables
        $test_vars = [
            ['price', 'Price', '10.50'],
            ['tax_rate', 'Tax Rate', '0.1'],
            ['discount', 'Discount', '5.00']
        ];
        
        foreach ($test_vars as $var) {
            $stmt = $pdo->prepare("SELECT * FROM wp_cfb_variables WHERE name = ?");
            $stmt->execute([$var[0]]);
            if (!$stmt->fetch()) {
                $stmt = $pdo->prepare("INSERT INTO wp_cfb_variables (name, label, value, is_active) VALUES (?, ?, ?, ?)");
                $stmt->execute([$var[0], $var[1], $var[2], 1]);
                echo "✅ Created {$var[0]} variable<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error creating test variables: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "<br>";
}
?>
