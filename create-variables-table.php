<?php
/**
 * Create Variables Table
 * Ensure the variables table exists and has the required data
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>🗄️ Create Variables Table & Data</h1>";

global $wpdb;

// Check if variables table exists
$table_name = $wpdb->prefix . 'cfb_variables';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

echo "<h2>1. 📋 Check Variables Table</h2>";

if (!$table_exists) {
    echo "<p style='color: orange;'>⚠️ Variables table doesn't exist. Creating it...</p>";
    
    // Create the variables table
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        label varchar(255) NOT NULL,
        value decimal(10,2) DEFAULT 0,
        is_active tinyint(1) DEFAULT 1,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY name (name)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Check if table was created
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ Variables table created successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create variables table!</p>";
        exit;
    }
} else {
    echo "<p style='color: green;'>✅ Variables table already exists</p>";
}

echo "<h2>2. 📊 Check/Create Required Variables</h2>";

// Define the required variables for your formula
$required_variables = array(
    array(
        'name' => 'dropdown_4',
        'label' => 'Dropdown 4',
        'value' => 6000
    ),
    array(
        'name' => 'paper',
        'label' => 'Paper Cost',
        'value' => 105000
    ),
    array(
        'name' => 'print',
        'label' => 'Print Cost',
        'value' => 5000000
    )
);

foreach ($required_variables as $var) {
    // Check if variable exists
    $existing = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE name = %s",
        $var['name']
    ));
    
    if ($existing) {
        echo "<p>✅ Variable <strong>{$var['name']}</strong> already exists (value: {$existing->value}, active: " . ($existing->is_active ? 'Yes' : 'No') . ")</p>";
        
        // Update if inactive
        if (!$existing->is_active) {
            $wpdb->update(
                $table_name,
                array('is_active' => 1),
                array('id' => $existing->id),
                array('%d'),
                array('%d')
            );
            echo "<p style='color: green;'>  → Activated variable {$var['name']}</p>";
        }
    } else {
        // Create the variable
        $result = $wpdb->insert(
            $table_name,
            array(
                'name' => $var['name'],
                'label' => $var['label'],
                'value' => $var['value'],
                'is_active' => 1,
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%f', '%d', '%s')
        );
        
        if ($result) {
            echo "<p style='color: green;'>✅ Created variable <strong>{$var['name']}</strong> with value {$var['value']}</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create variable {$var['name']}</p>";
        }
    }
}

echo "<h2>3. 📋 Current Variables in Database</h2>";

$all_variables = $wpdb->get_results("SELECT * FROM $table_name ORDER BY name");

if (empty($all_variables)) {
    echo "<p style='color: orange;'>⚠️ No variables found in database</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Name</th>";
    echo "<th style='padding: 10px;'>Label</th>";
    echo "<th style='padding: 10px;'>Value</th>";
    echo "<th style='padding: 10px;'>Active</th>";
    echo "<th style='padding: 10px;'>Created</th>";
    echo "</tr>";
    
    foreach ($all_variables as $var) {
        $status_color = $var->is_active ? '#d4edda' : '#f8d7da';
        $status_text = $var->is_active ? 'Yes' : 'No';
        
        echo "<tr style='background: $status_color;'>";
        echo "<td style='padding: 10px;'><strong>{$var->name}</strong></td>";
        echo "<td style='padding: 10px;'>{$var->label}</td>";
        echo "<td style='padding: 10px;'>{$var->value}</td>";
        echo "<td style='padding: 10px;'>$status_text</td>";
        echo "<td style='padding: 10px;'>{$var->created_at}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

echo "<h2>4. 🧪 Test Formula with Variables</h2>";

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    
    // Load variables from database
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    
    $variables = $wpdb->get_results("SELECT name, value FROM $table_name WHERE is_active = 1");
    $var_array = array();
    foreach ($variables as $var) {
        $var_array[$var->name] = floatval($var->value);
    }
    $property->setValue($formula_engine, $var_array);
    
    echo "<h3>Variables loaded:</h3>";
    echo "<pre>" . print_r($var_array, true) . "</pre>";
    
    // Test the formula
    $formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))';
    echo "<h3>Testing formula:</h3>";
    echo "<p><code>$formula</code></p>";
    
    $result = $formula_engine->evaluate_formula($formula);
    echo "<p><strong>Result:</strong> <span style='color: green; font-size: 24px;'>$result</span></p>";
    
    if ($result == 0) {
        echo "<p style='color: red; font-size: 18px;'>❌ Still returning 0 - there's still an issue with the formula calculation</p>";
    } else {
        echo "<p style='color: green; font-size: 18px;'>✅ Formula is working! This should fix the '---' display</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Formula test error: " . $e->getMessage() . "</p>";
}

echo "<h2>5. 🎯 Next Steps</h2>";
echo "<ol>";
echo "<li><strong>Variables are now set up correctly</strong></li>";
echo "<li><strong>Test your frontend form</strong> - the '---' should now show calculated values</li>";
echo "<li><strong>If still showing '---'</strong>, use the AI Settings page to debug further</li>";
echo "<li><strong>Check that your form has calculation fields</strong> properly configured</li>";
echo "</ol>";

echo "<div style='border: 3px solid #28a745; padding: 20px; margin: 20px 0; background: #f8fff8;'>";
echo "<h3 style='color: #28a745; margin: 0;'>🎉 Variables Setup Complete!</h3>";
echo "<p>The required variables for your formula are now in the database and active.</p>";
echo "<p>Test your form - the calculation should now work correctly!</p>";
echo "</div>";
?>
