/**
 * CFB Calculator Formula Builder
 * Professional formula builder with syntax highlighting and validation
 */

(function($) {
    'use strict';

    class CFBFormulaBuilder {
        constructor(container, options = {}) {
            this.container = $(container);
            this.options = {
                fields: [],
                functions: ['ceil', 'floor', 'min', 'max', 'pow', 'sqrt', 'abs', 'round', 'if', 'ifelse'],
                operators: ['+', '-', '*', '/', '(', ')'],
                variables: ['subtotal_1', 'subtotal_2', 'subtotal_3', 'subtotal_4', 'subtotal_5'],
                ...options
            };
            
            this.init();
        }

        init() {
            this.render();
            this.bindEvents();
            this.setupAutocomplete();
        }

        render() {
            const html = `
                <div class="cfb-formula-builder">
                    <div class="cfb-formula-header">
                        <h4>Formula Builder</h4>
                        <div class="cfb-formula-actions">
                            <button type="button" class="cfb-validate-formula button">Validate</button>
                            <button type="button" class="cfb-clear-formula button">Clear</button>
                        </div>
                    </div>
                    
                    <div class="cfb-formula-editor">
                        <textarea class="cfb-formula-input" placeholder="Enter your formula here..."></textarea>
                        <div class="cfb-formula-overlay"></div>
                    </div>
                    
                    <div class="cfb-formula-toolbar">
                        <div class="cfb-toolbar-section">
                            <label>Fields:</label>
                            <div class="cfb-toolbar-items cfb-fields-list">
                                ${this.renderFieldButtons()}
                            </div>
                        </div>
                        
                        <div class="cfb-toolbar-section">
                            <label>Functions:</label>
                            <div class="cfb-toolbar-items cfb-functions-list">
                                ${this.renderFunctionButtons()}
                            </div>
                        </div>
                        
                        <div class="cfb-toolbar-section">
                            <label>Operators:</label>
                            <div class="cfb-toolbar-items cfb-operators-list">
                                ${this.renderOperatorButtons()}
                            </div>
                        </div>
                        
                        <div class="cfb-toolbar-section">
                            <label>Variables:</label>
                            <div class="cfb-toolbar-items cfb-variables-list">
                                ${this.renderVariableButtons()}
                            </div>
                        </div>
                    </div>
                    
                    <div class="cfb-formula-help">
                        <div class="cfb-help-toggle">
                            <button type="button" class="cfb-toggle-help">
                                <span class="dashicons dashicons-editor-help"></span>
                                Formula Help
                            </button>
                        </div>
                        <div class="cfb-help-content" style="display: none;">
                            ${this.renderHelpContent()}
                        </div>
                    </div>
                    
                    <div class="cfb-formula-validation">
                        <div class="cfb-validation-result"></div>
                    </div>
                </div>
            `;
            
            this.container.html(html);
            this.textarea = this.container.find('.cfb-formula-input');
            this.overlay = this.container.find('.cfb-formula-overlay');
            this.validation = this.container.find('.cfb-validation-result');
        }

        renderFieldButtons() {
            return this.options.fields.map(field => 
                `<button type="button" class="cfb-insert-btn cfb-field-btn" data-insert="{${field.name}}" title="${field.label}">
                    ${field.label}
                </button>`
            ).join('');
        }

        renderFunctionButtons() {
            const functionDescriptions = {
                'ceil': 'Round up to nearest integer',
                'floor': 'Round down to nearest integer',
                'min': 'Minimum value',
                'max': 'Maximum value',
                'pow': 'Power/exponent',
                'sqrt': 'Square root',
                'abs': 'Absolute value',
                'round': 'Round to nearest integer',
                'if': 'Conditional logic',
                'ifelse': 'If-else condition'
            };

            return this.options.functions.map(func => 
                `<button type="button" class="cfb-insert-btn cfb-function-btn" data-insert="${func}(" title="${functionDescriptions[func] || func}">
                    ${func}()
                </button>`
            ).join('');
        }

        renderOperatorButtons() {
            const operatorDescriptions = {
                '+': 'Addition',
                '-': 'Subtraction',
                '*': 'Multiplication',
                '/': 'Division',
                '(': 'Open parenthesis',
                ')': 'Close parenthesis'
            };

            return this.options.operators.map(op => 
                `<button type="button" class="cfb-insert-btn cfb-operator-btn" data-insert="${op}" title="${operatorDescriptions[op]}">
                    ${op}
                </button>`
            ).join('');
        }

        renderVariableButtons() {
            return this.options.variables.map(variable => 
                `<button type="button" class="cfb-insert-btn cfb-variable-btn" data-insert="${variable}" title="Subtotal variable">
                    ${variable}
                </button>`
            ).join('');
        }

        renderHelpContent() {
            return `
                <div class="cfb-help-sections">
                    <div class="cfb-help-section">
                        <h5>Basic Operations</h5>
                        <ul>
                            <li><code>+</code> Addition</li>
                            <li><code>-</code> Subtraction</li>
                            <li><code>*</code> Multiplication</li>
                            <li><code>/</code> Division</li>
                            <li><code>()</code> Parentheses for grouping</li>
                        </ul>
                    </div>
                    
                    <div class="cfb-help-section">
                        <h5>Functions</h5>
                        <ul>
                            <li><code>ceil(x)</code> Round up to nearest integer</li>
                            <li><code>floor(x)</code> Round down to nearest integer</li>
                            <li><code>min(a,b,c...)</code> Minimum value</li>
                            <li><code>max(a,b,c...)</code> Maximum value</li>
                            <li><code>pow(x,y)</code> x to the power of y</li>
                            <li><code>if(condition,true_value,false_value)</code> Conditional</li>
                        </ul>
                    </div>
                    
                    <div class="cfb-help-section">
                        <h5>Examples</h5>
                        <ul>
                            <li><code>{quantity} * {price}</code> Basic multiplication</li>
                            <li><code>ceil({total} * 1.1)</code> Add 10% and round up</li>
                            <li><code>if({quantity} > 10, {price} * 0.9, {price})</code> Bulk discount</li>
                            <li><code>max({base_price}, 50)</code> Minimum price of 50</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        bindEvents() {
            // Insert buttons
            this.container.on('click', '.cfb-insert-btn', (e) => {
                const insertText = $(e.target).data('insert');
                this.insertAtCursor(insertText);
            });

            // Validate formula
            this.container.on('click', '.cfb-validate-formula', () => {
                this.validateFormula();
            });

            // Clear formula
            this.container.on('click', '.cfb-clear-formula', () => {
                this.textarea.val('').trigger('input');
            });

            // Toggle help
            this.container.on('click', '.cfb-toggle-help', () => {
                this.container.find('.cfb-help-content').slideToggle();
            });

            // Real-time validation
            this.textarea.on('input', () => {
                this.highlightSyntax();
                this.validateFormula();
            });

            // Handle keyboard shortcuts
            this.textarea.on('keydown', (e) => {
                this.handleKeyboardShortcuts(e);
            });
        }

        setupAutocomplete() {
            // Simple autocomplete for field names and functions
            this.textarea.on('input', (e) => {
                const cursorPos = e.target.selectionStart;
                const text = e.target.value;
                const beforeCursor = text.substring(0, cursorPos);
                
                // Check if we're typing a field name
                const fieldMatch = beforeCursor.match(/\{([^}]*)$/);
                if (fieldMatch) {
                    this.showFieldSuggestions(fieldMatch[1], cursorPos);
                    return;
                }
                
                // Check if we're typing a function
                const funcMatch = beforeCursor.match(/([a-zA-Z]+)$/);
                if (funcMatch) {
                    this.showFunctionSuggestions(funcMatch[1], cursorPos);
                    return;
                }
                
                this.hideSuggestions();
            });
        }

        insertAtCursor(text) {
            const textarea = this.textarea[0];
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const value = textarea.value;
            
            const newValue = value.substring(0, start) + text + value.substring(end);
            textarea.value = newValue;
            
            // Set cursor position after inserted text
            const newCursorPos = start + text.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            
            this.textarea.trigger('input');
            textarea.focus();
        }

        highlightSyntax() {
            const text = this.textarea.val();
            let highlighted = text;
            
            // Highlight field names
            highlighted = highlighted.replace(/\{([^}]+)\}/g, '<span class="cfb-field-highlight">{$1}</span>');
            
            // Highlight functions
            this.options.functions.forEach(func => {
                const regex = new RegExp(`\\b(${func})\\(`, 'g');
                highlighted = highlighted.replace(regex, '<span class="cfb-function-highlight">$1</span>(');
            });
            
            // Highlight operators
            highlighted = highlighted.replace(/([+\-*/()])/g, '<span class="cfb-operator-highlight">$1</span>');
            
            // Highlight numbers
            highlighted = highlighted.replace(/\b(\d+\.?\d*)\b/g, '<span class="cfb-number-highlight">$1</span>');
            
            this.overlay.html(highlighted);
        }

        validateFormula() {
            const formula = this.textarea.val().trim();
            
            if (!formula) {
                this.showValidationResult('', 'info');
                return;
            }
            
            try {
                const errors = this.checkSyntaxErrors(formula);
                
                if (errors.length > 0) {
                    this.showValidationResult('Syntax errors: ' + errors.join(', '), 'error');
                } else {
                    this.showValidationResult('Formula syntax is valid', 'success');
                }
            } catch (error) {
                this.showValidationResult('Invalid formula: ' + error.message, 'error');
            }
        }

        checkSyntaxErrors(formula) {
            const errors = [];
            
            // Check balanced parentheses
            let parenCount = 0;
            for (let char of formula) {
                if (char === '(') parenCount++;
                if (char === ')') parenCount--;
                if (parenCount < 0) {
                    errors.push('Unmatched closing parenthesis');
                    break;
                }
            }
            if (parenCount > 0) {
                errors.push('Unmatched opening parenthesis');
            }
            
            // Check for empty field references
            const emptyFields = formula.match(/\{\s*\}/g);
            if (emptyFields) {
                errors.push('Empty field references found');
            }
            
            // Check for invalid characters
            const invalidChars = formula.match(/[^a-zA-Z0-9+\-*/(){}.,_\s]/g);
            if (invalidChars) {
                errors.push('Invalid characters: ' + [...new Set(invalidChars)].join(', '));
            }
            
            return errors;
        }

        showValidationResult(message, type) {
            this.validation.removeClass('cfb-validation-success cfb-validation-error cfb-validation-info');
            
            if (type) {
                this.validation.addClass(`cfb-validation-${type}`);
            }
            
            this.validation.text(message);
        }

        handleKeyboardShortcuts(e) {
            // Ctrl+Space for autocomplete
            if (e.ctrlKey && e.code === 'Space') {
                e.preventDefault();
                this.showAllSuggestions();
            }
            
            // Tab for indentation
            if (e.key === 'Tab') {
                e.preventDefault();
                this.insertAtCursor('  ');
            }
        }

        updateFields(fields) {
            this.options.fields = fields;
            this.container.find('.cfb-fields-list').html(this.renderFieldButtons());
        }

        getValue() {
            return this.textarea.val();
        }

        setValue(value) {
            this.textarea.val(value).trigger('input');
        }

        showFieldSuggestions(partial, cursorPos) {
            // Implementation for field suggestions dropdown
        }

        showFunctionSuggestions(partial, cursorPos) {
            // Implementation for function suggestions dropdown
        }

        showAllSuggestions() {
            // Implementation for showing all available suggestions
        }

        hideSuggestions() {
            // Implementation for hiding suggestions dropdown
        }
    }

    // Make it globally available
    window.CFBFormulaBuilder = CFBFormulaBuilder;

    // jQuery plugin
    $.fn.cfbFormulaBuilder = function(options) {
        return this.each(function() {
            if (!$(this).data('cfb-formula-builder')) {
                $(this).data('cfb-formula-builder', new CFBFormulaBuilder(this, options));
            }
        });
    };

})(jQuery);
