(function($) {
    'use strict';

    class CFBVariables {
        constructor() {
            this.modal = $('#cfb-variable-modal');
            this.form = $('#cfb-variable-form');
            this.init();
        }

        init() {
            this.bindEvents();
        }

        bindEvents() {
            // Add variable button
            $(document).on('click', '.cfb-add-variable-btn', () => this.showAddModal());
            
            // Edit variable button
            $(document).on('click', '.cfb-edit-variable', (e) => this.showEditModal(e));
            
            // Delete variable button
            $(document).on('click', '.cfb-delete-variable', (e) => this.deleteVariable(e));
            
            // Modal close
            $(document).on('click', '.cfb-modal-close, #cfb-cancel-variable', () => this.closeModal());
            
            // Save variable
            $(document).on('click', '#cfb-save-variable', () => this.saveVariable());
            
            // Form submission
            this.form.on('submit', (e) => {
                e.preventDefault();
                this.saveVariable();
            });
            
            // Auto-generate variable name from label
            $('#variable-label').on('input', () => this.autoGenerateName());
            
            // Modal backdrop click
            this.modal.on('click', (e) => {
                if (e.target === this.modal[0]) {
                    this.closeModal();
                }
            });
        }

        showAddModal() {
            this.resetForm();
            $('#cfb-modal-title').text('Add New Variable');
            this.modal.show();
        }

        showEditModal(e) {
            const variableId = $(e.currentTarget).data('variable-id');
            this.loadVariable(variableId);
        }

        loadVariable(variableId) {
            const card = $(`.cfb-variable-card[data-variable-id="${variableId}"]`);
            
            // Extract data from the card
            const name = card.find('.cfb-variable-code').text().replace(/[{}]/g, '');
            const label = card.find('.cfb-variable-name').text();
            const value = card.find('.cfb-variable-value-amount').text().replace(/[,]/g, '');
            const description = card.find('.cfb-variable-description').text();
            const category = card.find('.cfb-variable-category').text().toLowerCase();
            const isActive = card.find('.cfb-variable-status').hasClass('active');
            
            // Get color and icon from the header
            const color = card.find('.cfb-variable-header').css('border-left-color') || '#667eea';
            const iconClass = card.find('.cfb-variable-icon .dashicons').attr('class');
            const icon = iconClass ? iconClass.replace('dashicons ', '') : 'dashicons-admin-settings';
            
            // Populate form
            $('#variable-id').val(variableId);
            $('#variable-name').val(name);
            $('#variable-label').val(label);
            $('#variable-value').val(parseFloat(value) || 0);
            $('#variable-description').val(description);
            $('#variable-category').val(category);
            $('#variable-icon').val(icon);
            $('#variable-color').val(this.rgbToHex(color));
            $('#variable-active').prop('checked', isActive);
            
            $('#cfb-modal-title').text('Edit Variable');
            this.modal.show();
        }

        deleteVariable(e) {
            const variableId = $(e.currentTarget).data('variable-id');
            const variableName = $(e.currentTarget).closest('.cfb-variable-card').find('.cfb-variable-name').text();
            
            if (!confirm(`Are you sure you want to delete the variable "${variableName}"? This action cannot be undone.`)) {
                return;
            }
            
            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_delete_variable',
                    nonce: cfb_admin_ajax.nonce,
                    variable_id: variableId
                },
                success: (response) => {
                    if (response.success) {
                        $(`.cfb-variable-card[data-variable-id="${variableId}"]`).fadeOut(() => {
                            $(`.cfb-variable-card[data-variable-id="${variableId}"]`).remove();
                            this.checkEmptyState();
                        });
                        this.showMessage(response.data, 'success');
                    } else {
                        this.showMessage(response.data || 'Failed to delete variable', 'error');
                    }
                },
                error: () => {
                    this.showMessage('Network error occurred', 'error');
                }
            });
        }

        saveVariable() {
            const formData = {
                action: 'cfb_save_variable',
                nonce: cfb_admin_ajax.nonce,
                id: $('#variable-id').val(),
                name: $('#variable-name').val(),
                label: $('#variable-label').val(),
                value: $('#variable-value').val(),
                description: $('#variable-description').val(),
                category: $('#variable-category').val(),
                icon: $('#variable-icon').val(),
                color: $('#variable-color').val(),
                is_active: $('#variable-active').is(':checked') ? 1 : 0
            };
            
            // Validate required fields
            if (!formData.name || !formData.label || !formData.value) {
                this.showMessage('Please fill in all required fields', 'error');
                return;
            }
            
            // Validate variable name format
            if (!/^[a-z_][a-z0-9_]*$/.test(formData.name)) {
                this.showMessage('Variable name must contain only lowercase letters, numbers, and underscores', 'error');
                return;
            }
            
            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: formData,
                success: (response) => {
                    if (response.success) {
                        this.closeModal();
                        this.showMessage(response.data.message, 'success');
                        // Reload the page to show updated variables
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        this.showMessage(response.data || 'Failed to save variable', 'error');
                    }
                },
                error: () => {
                    this.showMessage('Network error occurred', 'error');
                }
            });
        }

        closeModal() {
            this.modal.hide();
            this.resetForm();
        }

        resetForm() {
            this.form[0].reset();
            $('#variable-id').val('');
            $('#variable-color').val('#667eea');
            $('#variable-active').prop('checked', true);
        }

        autoGenerateName() {
            const label = $('#variable-label').val();
            const name = label
                .toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '_')
                .replace(/^_+|_+$/g, '');
            
            if (name && !$('#variable-name').val()) {
                $('#variable-name').val(name);
            }
        }

        checkEmptyState() {
            if ($('.cfb-variable-card').length === 0) {
                const emptyState = `
                    <div class="cfb-no-variables">
                        <div class="cfb-no-variables-icon">
                            <span class="dashicons dashicons-admin-settings"></span>
                        </div>
                        <h3>No Variables Yet</h3>
                        <p>Create your first global variable to use in calculator formulas.</p>
                        <button type="button" class="button-primary cfb-add-variable-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            Add Your First Variable
                        </button>
                    </div>
                `;
                $('#cfb-variables-grid').html(emptyState);
            }
        }

        rgbToHex(rgb) {
            if (rgb.startsWith('#')) return rgb;
            
            const result = rgb.match(/\d+/g);
            if (!result || result.length < 3) return '#667eea';
            
            return '#' + result.slice(0, 3).map(x => {
                const hex = parseInt(x).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            }).join('');
        }

        showMessage(message, type) {
            const messageClass = type === 'success' ? 'notice-success' : 'notice-error';
            const messageHtml = `
                <div class="notice ${messageClass} is-dismissible">
                    <p>${message}</p>
                </div>
            `;
            
            $('.cfb-variables-header').after(messageHtml);
            
            setTimeout(() => {
                $('.notice').fadeOut(() => {
                    $('.notice').remove();
                });
            }, 3000);
        }
    }

    // Initialize when document is ready
    $(document).ready(() => {
        if ($('.cfb-variables-page').length) {
            new CFBVariables();
        }
    });

})(jQuery);
