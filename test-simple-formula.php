<?php
/**
 * Simple Formula Test
 * Testing basic functionality step by step
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>Simple Formula Test</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "✅ Formula engine created<br>";
    
    // Set up a simple variable
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $property->setValue($formula_engine, array('dropdown_4' => 6000));
    
    echo "<h2>Testing Step by Step:</h2>";
    
    // Test 1: Simple variable
    echo "<h3>1. Simple Variable Test:</h3>";
    $result = $formula_engine->evaluate_formula('{dropdown_4}');
    echo "Formula: {dropdown_4} = $result<br>";
    
    // Test 2: Simple math
    echo "<h3>2. Simple Math Test:</h3>";
    $result = $formula_engine->evaluate_formula('2 + 3');
    echo "Formula: 2 + 3 = $result<br>";
    
    // Test 3: Variable with math
    echo "<h3>3. Variable with Math:</h3>";
    $result = $formula_engine->evaluate_formula('{dropdown_4} + 100');
    echo "Formula: {dropdown_4} + 100 = $result<br>";
    
    // Test 4: More complex
    echo "<h3>4. More Complex:</h3>";
    $result = $formula_engine->evaluate_formula('{dropdown_4} + 100 - 5000');
    echo "Formula: {dropdown_4} + 100 - 5000 = $result<br>";
    
    // Test 5: Division
    echo "<h3>5. Division:</h3>";
    $result = $formula_engine->evaluate_formula('({dropdown_4} + 100 - 5000) / 1000');
    echo "Formula: ({dropdown_4} + 100 - 5000) / 1000 = $result<br>";
    
    // Test 6: Ceil function
    echo "<h3>6. Ceil Function:</h3>";
    $result = $formula_engine->evaluate_formula('ceil(1.1)');
    echo "Formula: ceil(1.1) = $result<br>";
    
    // Test 7: Ceil with variable
    echo "<h3>7. Ceil with Variable:</h3>";
    $result = $formula_engine->evaluate_formula('ceil(({dropdown_4} + 100 - 5000) / 1000)');
    echo "Formula: ceil(({dropdown_4} + 100 - 5000) / 1000) = $result<br>";
    
    // Test 8: Max function
    echo "<h3>8. Max Function:</h3>";
    $result = $formula_engine->evaluate_formula('max(0, 2)');
    echo "Formula: max(0, 2) = $result<br>";
    
    // Test 9: Max with ceil
    echo "<h3>9. Max with Ceil:</h3>";
    $result = $formula_engine->evaluate_formula('max(0, ceil(1.1))');
    echo "Formula: max(0, ceil(1.1)) = $result<br>";
    
    // Test 10: Full nested function
    echo "<h3>10. Full Nested Function:</h3>";
    $result = $formula_engine->evaluate_formula('max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))');
    echo "Formula: max(0, ceil(({dropdown_4} + 100 - 5000) / 1000)) = $result<br>";
    
    // Test 11: With multiplication
    echo "<h3>11. With Multiplication:</h3>";
    $result = $formula_engine->evaluate_formula('0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))');
    echo "Formula: 0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000)) = $result<br>";
    
    // Test 12: Full formula
    echo "<h3>12. Full Formula:</h3>";
    $result = $formula_engine->evaluate_formula('5000000 * (1 + 0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000)))');
    echo "Formula: 5000000 * (1 + 0.2 * max(0, ceil(({dropdown_4} + 100 - 5000) / 1000))) = $result<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<h2>Expected Results:</h2>";
echo "dropdown_4 = 6000<br>";
echo "6000 + 100 - 5000 = 1100<br>";
echo "1100 / 1000 = 1.1<br>";
echo "ceil(1.1) = 2<br>";
echo "max(0, 2) = 2<br>";
echo "0.2 * 2 = 0.4<br>";
echo "1 + 0.4 = 1.4<br>";
echo "5000000 * 1.4 = 7000000<br>";
?>
