<?php
/**
 * CFB Calculator Frontend
 * Handles frontend form display and user interactions
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Frontend {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Constructor
    }
    
    /**
     * Render calculator form
     */
    public function render_calculator($form_id, $show_title = true) {
        if (!$form_id) {
            return '<p>' . __('Invalid form ID', 'cfb-calculator') . '</p>';
        }
        
        $form = CFB_Database::get_instance()->get_form($form_id);
        if (!$form) {
            return '<p>' . __('Form not found', 'cfb-calculator') . '</p>';
        }
        
        $form_data = json_decode($form->form_data, true);
        $settings = json_decode($form->settings, true);
        
        ob_start();
        ?>
        <div class="cfb-calculator-wrapper" data-form-id="<?php echo esc_attr($form_id); ?>">
            <?php if ($show_title && !empty($form->name)): ?>
                <h3 class="cfb-calculator-title"><?php echo esc_html($form->name); ?></h3>
            <?php endif; ?>
            
            <?php if (!empty($form->description)): ?>
                <div class="cfb-calculator-description">
                    <?php echo wp_kses_post($form->description); ?>
                </div>
            <?php endif; ?>
            
            <form class="cfb-calculator-form" id="cfb-form-<?php echo esc_attr($form_id); ?>">
                <div class="cfb-form-fields">
                    <?php
                    if (isset($form_data['fields']) && is_array($form_data['fields'])) {
                        foreach ($form_data['fields'] as $field) {
                            $this->render_field($field);
                        }
                    }
                    ?>
                </div>
                
                <?php if (isset($form_data['enable_subtotals']) && $form_data['enable_subtotals']): ?>
                    <div class="cfb-calculation-results">
                        <?php if (isset($form_data['subtotals']) && is_array($form_data['subtotals'])): ?>
                            <div class="cfb-subtotals">
                                <h4><?php _e('Breakdown', 'cfb-calculator'); ?></h4>
                                <?php foreach ($form_data['subtotals'] as $index => $subtotal): ?>
                                    <div class="cfb-subtotal-line" data-subtotal="<?php echo $index; ?>">
                                        <span class="cfb-subtotal-label"><?php echo esc_html($subtotal['label']); ?></span>
                                        <span class="cfb-subtotal-value">-</span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="cfb-total-section">
                            <div class="cfb-total-line">
                                <span class="cfb-total-label"><?php _e('Total', 'cfb-calculator'); ?></span>
                                <span class="cfb-total-value">-</span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="cfb-form-actions">
                    <button type="button" class="cfb-calculate-btn"><?php _e('Calculate', 'cfb-calculator'); ?></button>
                    <button type="button" class="cfb-reset-btn"><?php _e('Reset', 'cfb-calculator'); ?></button>
                </div>

                <!-- Invoice Section (hidden initially) -->
                <div class="cfb-invoice-section" style="display: none;">
                    <div class="cfb-invoice-checkbox">
                        <label>
                            <input type="checkbox" id="cfb-want-invoice" name="want_invoice">
                            <span class="cfb-checkbox-text"><?php _e('Do you want an invoice?', 'cfb-calculator'); ?></span>
                        </label>
                    </div>

                    <div class="cfb-invoice-form" style="display: none;">
                        <h3><?php _e('Invoice Information', 'cfb-calculator'); ?></h3>
                        <div class="cfb-invoice-fields">
                            <div class="cfb-field-row">
                                <div class="cfb-field-col">
                                    <label for="cfb-invoice-name"><?php _e('Full Name', 'cfb-calculator'); ?> *</label>
                                    <input type="text" id="cfb-invoice-name" name="invoice_name" required>
                                </div>
                                <div class="cfb-field-col">
                                    <label for="cfb-invoice-email"><?php _e('Email Address', 'cfb-calculator'); ?> *</label>
                                    <input type="email" id="cfb-invoice-email" name="invoice_email" required>
                                </div>
                            </div>
                            <div class="cfb-field-row">
                                <div class="cfb-field-col">
                                    <label for="cfb-invoice-phone"><?php _e('Phone Number', 'cfb-calculator'); ?></label>
                                    <input type="tel" id="cfb-invoice-phone" name="invoice_phone">
                                </div>
                                <div class="cfb-field-col">
                                    <label for="cfb-invoice-address"><?php _e('Address', 'cfb-calculator'); ?></label>
                                    <textarea id="cfb-invoice-address" name="invoice_address" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="cfb-invoice-actions">
                                <button type="button" class="cfb-generate-invoice-btn">
                                    <?php _e('Generate PDF Invoice', 'cfb-calculator'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cfb-loading" style="display: none;">
                    <span class="cfb-spinner"></span>
                    <?php _e('Calculating...', 'cfb-calculator'); ?>
                </div>

                <div class="cfb-error-message" style="display: none;"></div>
            </form>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render individual form field
     */
    private function render_field($field) {
        $field_id = 'cfb-field-' . $field['name'];
        $field_class = 'cfb-field cfb-field-' . $field['type'];

        // Add required class
        if (isset($field['required']) && $field['required']) {
            $field_class .= ' cfb-field-required';
        }

        // Add width and position classes
        if (isset($field['width'])) {
            $field_class .= ' cfb-field-width-' . $field['width'];
        }
        if (isset($field['position'])) {
            $field_class .= ' cfb-field-position-' . $field['position'];
        }

        // Add conditional logic data attributes
        $conditional_attrs = '';
        if (isset($field['conditional_logic']) && $field['conditional_logic']['enabled']) {
            $conditional_attrs = 'data-conditional="' . esc_attr(wp_json_encode($field['conditional_logic'])) . '"';
        }
        
        ?>
        <div class="<?php echo esc_attr($field_class); ?>" <?php echo $conditional_attrs; ?>>
            <label for="<?php echo esc_attr($field_id); ?>" class="cfb-field-label">
                <?php echo esc_html($field['label']); ?>
                <?php if (isset($field['required']) && $field['required']): ?>
                    <span class="cfb-required">*</span>
                <?php endif; ?>
            </label>
            
            <div class="cfb-field-input">
                <?php $this->render_field_input($field, $field_id); ?>
            </div>
            
            <?php if (isset($field['description']) && !empty($field['description'])): ?>
                <div class="cfb-field-description">
                    <?php echo wp_kses_post($field['description']); ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    /**
     * Render field input based on type
     */
    private function render_field_input($field, $field_id) {
        $field_name = $field['name'];
        $required = isset($field['required']) && $field['required'] ? 'required' : '';
        
        switch ($field['type']) {
            case 'text':
                ?>
                <input type="text" 
                       id="<?php echo esc_attr($field_id); ?>" 
                       name="<?php echo esc_attr($field_name); ?>" 
                       placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
                       <?php echo $required; ?>
                       class="cfb-text-input">
                <?php
                break;
                
            case 'number':
                $min = isset($field['min']) ? 'min="' . esc_attr($field['min']) . '"' : '';
                $max = isset($field['max']) ? 'max="' . esc_attr($field['max']) . '"' : '';
                $step = isset($field['step']) ? 'step="' . esc_attr($field['step']) . '"' : '';
                $default = isset($field['default_value']) ? 'value="' . esc_attr($field['default_value']) . '"' : '';
                ?>
                <input type="number" 
                       id="<?php echo esc_attr($field_id); ?>" 
                       name="<?php echo esc_attr($field_name); ?>" 
                       <?php echo $min . ' ' . $max . ' ' . $step . ' ' . $default; ?>
                       <?php echo $required; ?>
                       class="cfb-number-input">
                <?php
                break;
                
            case 'slider':
                $min = isset($field['min']) ? $field['min'] : 0;
                $max = isset($field['max']) ? $field['max'] : 100;
                $step = isset($field['step']) ? $field['step'] : 1;
                $default = isset($field['default_value']) ? $field['default_value'] : $min;
                $show_value = isset($field['show_value']) && $field['show_value'];
                ?>
                <div class="cfb-slider-container">
                    <input type="range" 
                           id="<?php echo esc_attr($field_id); ?>" 
                           name="<?php echo esc_attr($field_name); ?>" 
                           min="<?php echo esc_attr($min); ?>"
                           max="<?php echo esc_attr($max); ?>"
                           step="<?php echo esc_attr($step); ?>"
                           value="<?php echo esc_attr($default); ?>"
                           class="cfb-slider-input">
                    <?php if ($show_value): ?>
                        <div class="cfb-slider-value">
                            <span class="cfb-slider-current"><?php echo esc_html($default); ?></span>
                            <span class="cfb-slider-range">(<?php echo esc_html($min); ?> - <?php echo esc_html($max); ?>)</span>
                        </div>
                    <?php endif; ?>
                </div>
                <?php
                break;
                
            case 'dropdown':
                ?>
                <select id="<?php echo esc_attr($field_id); ?>" 
                        name="<?php echo esc_attr($field_name); ?>" 
                        <?php echo $required; ?>
                        class="cfb-select-input">
                    <?php if (isset($field['placeholder']) && !empty($field['placeholder'])): ?>
                        <option value=""><?php echo esc_html($field['placeholder']); ?></option>
                    <?php endif; ?>
                    <?php if (isset($field['options']) && is_array($field['options'])): ?>
                        <?php foreach ($field['options'] as $option): ?>
                            <option value="<?php echo esc_attr($option['value']); ?>">
                                <?php echo esc_html($option['label']); ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
                <?php
                break;
                
            case 'radio':
                ?>
                <div class="cfb-radio-group">
                    <?php if (isset($field['options']) && is_array($field['options'])): ?>
                        <?php foreach ($field['options'] as $index => $option): ?>
                            <label class="cfb-radio-option">
                                <input type="radio" 
                                       name="<?php echo esc_attr($field_name); ?>" 
                                       value="<?php echo esc_attr($option['value']); ?>"
                                       data-price="<?php echo esc_attr($option['price'] ?? 0); ?>"
                                       <?php echo $required; ?>>
                                <span class="cfb-radio-label">
                                    <?php echo esc_html($option['label']); ?>
                                    <?php if (isset($option['price']) && $option['price'] != 0): ?>
                                        <span class="cfb-option-price">(<?php echo $this->format_price($option['price']); ?>)</span>
                                    <?php endif; ?>
                                </span>
                            </label>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php
                break;
                
            case 'checkbox':
                ?>
                <div class="cfb-checkbox-group">
                    <?php if (isset($field['options']) && is_array($field['options'])): ?>
                        <?php foreach ($field['options'] as $index => $option): ?>
                            <label class="cfb-checkbox-option">
                                <input type="checkbox" 
                                       name="<?php echo esc_attr($field_name); ?>[]" 
                                       value="<?php echo esc_attr($option['value']); ?>"
                                       data-price="<?php echo esc_attr($option['price'] ?? 0); ?>">
                                <span class="cfb-checkbox-label">
                                    <?php echo esc_html($option['label']); ?>
                                    <?php if (isset($option['price']) && $option['price'] != 0): ?>
                                        <span class="cfb-option-price">(<?php echo $this->format_price($option['price']); ?>)</span>
                                    <?php endif; ?>
                                </span>
                            </label>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php
                break;

            case 'calculation':
            case 'total':
                $display_type = isset($field['display_type']) ? $field['display_type'] : 'number';
                $formula = isset($field['formula']) ? $field['formula'] : '';
                ?>
                <div class="cfb-calculation-field"
                     data-formula="<?php echo esc_attr($formula); ?>"
                     data-display-type="<?php echo esc_attr($display_type); ?>"
                     data-field-name="<?php echo esc_attr($field_name); ?>">
                    <div class="cfb-calculation-value">
                        <span class="cfb-calculated-amount">---</span>
                    </div>

                    <?php if ($field['type'] === 'total' && isset($field['show_breakdown']) && $field['show_breakdown']): ?>
                        <div class="cfb-calculation-breakdown" style="display: none;">
                            <div class="cfb-breakdown-details"></div>
                        </div>
                    <?php endif; ?>

                    <input type="hidden"
                           name="<?php echo esc_attr($field_name); ?>"
                           value="0"
                           class="cfb-calculation-input">
                </div>
                <?php
                break;
        }
    }
    
    /**
     * Format price for display
     */
    private function format_price($price) {
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $currency_position = get_option('cfb_currency_position', 'left');
        $decimal_places = get_option('cfb_decimal_places', 2);
        
        $formatted_price = number_format($price, $decimal_places);
        
        if ($currency_position === 'right') {
            return $formatted_price . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted_price;
        }
    }
    
    /**
     * Get form data for JavaScript
     */
    public function get_form_js_data($form_id) {
        $form = CFB_Database::get_instance()->get_form($form_id);
        if (!$form) {
            return array();
        }
        
        $form_data = json_decode($form->form_data, true);
        $settings = json_decode($form->settings, true);
        
        return array(
            'form_id' => $form_id,
            'fields' => $form_data['fields'] ?? array(),
            'subtotals' => $form_data['subtotals'] ?? array(),
            'total_formula' => $form_data['total_formula'] ?? '',
            'settings' => $settings
        );
    }
}
