<?php
/**
 * CFB Calculator Form Builder
 * Handles form creation and management in admin
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Form_Builder {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Constructor
    }
    
    /**
     * Get available field types
     */
    public function get_field_types() {
        return array(
            'text' => array(
                'label' => __('Text Field', 'cfb-calculator'),
                'icon' => 'dashicons-edit',
                'supports' => array('conditional_logic', 'calculation_rule')
            ),
            'number' => array(
                'label' => __('Number Field', 'cfb-calculator'),
                'icon' => 'dashicons-calculator',
                'supports' => array('conditional_logic', 'min_max')
            ),
            'slider' => array(
                'label' => __('Slider', 'cfb-calculator'),
                'icon' => 'dashicons-leftright',
                'supports' => array('conditional_logic', 'min_max', 'step')
            ),
            'dropdown' => array(
                'label' => __('Dropdown', 'cfb-calculator'),
                'icon' => 'dashicons-arrow-down-alt2',
                'supports' => array('conditional_logic', 'options')
            ),
            'radio' => array(
                'label' => __('Radio Buttons', 'cfb-calculator'),
                'icon' => 'dashicons-marker',
                'supports' => array('conditional_logic', 'options')
            ),
            'checkbox' => array(
                'label' => __('Checkboxes', 'cfb-calculator'),
                'icon' => 'dashicons-yes',
                'supports' => array('conditional_logic', 'options')
            ),
            'calculation' => array(
                'label' => __('Calculation Field', 'cfb-calculator'),
                'icon' => 'dashicons-chart-line',
                'supports' => array('conditional_logic', 'formula')
            ),
            'total' => array(
                'label' => __('Total Field', 'cfb-calculator'),
                'icon' => 'dashicons-money-alt',
                'supports' => array('conditional_logic', 'formula')
            )
        );
    }
    
    /**
     * Get default field configuration
     */
    public function get_default_field_config($type) {
        $defaults = array(
            'text' => array(
                'type' => 'text',
                'label' => __('Text Field', 'cfb-calculator'),
                'name' => 'text_field_' . time(),
                'placeholder' => '',
                'required' => false,
                'calculation_rule' => array(
                    'type' => 'per_character',
                    'rate' => 0
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'number' => array(
                'type' => 'number',
                'label' => __('Number Field', 'cfb-calculator'),
                'name' => 'number_field_' . time(),
                'min' => 0,
                'max' => 100,
                'step' => 1,
                'default_value' => 0,
                'required' => false,
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'slider' => array(
                'type' => 'slider',
                'label' => __('Slider', 'cfb-calculator'),
                'name' => 'slider_field_' . time(),
                'min' => 0,
                'max' => 100,
                'step' => 1,
                'default_value' => 50,
                'show_value' => true,
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'dropdown' => array(
                'type' => 'dropdown',
                'label' => __('Dropdown', 'cfb-calculator'),
                'name' => 'dropdown_field_' . time(),
                'placeholder' => __('Select an option', 'cfb-calculator'),
                'required' => false,
                'options' => array(
                    array('label' => __('Option 1', 'cfb-calculator'), 'value' => '1'),
                    array('label' => __('Option 2', 'cfb-calculator'), 'value' => '2')
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'radio' => array(
                'type' => 'radio',
                'label' => __('Radio Buttons', 'cfb-calculator'),
                'name' => 'radio_field_' . time(),
                'required' => false,
                'options' => array(
                    array('label' => __('Option 1', 'cfb-calculator'), 'value' => '1'),
                    array('label' => __('Option 2', 'cfb-calculator'), 'value' => '2')
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'checkbox' => array(
                'type' => 'checkbox',
                'label' => __('Checkboxes', 'cfb-calculator'),
                'name' => 'checkbox_field_' . time(),
                'options' => array(
                    array('label' => __('Option 1', 'cfb-calculator'), 'value' => '1'),
                    array('label' => __('Option 2', 'cfb-calculator'), 'value' => '2')
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'calculation' => array(
                'type' => 'calculation',
                'label' => __('Calculation', 'cfb-calculator'),
                'name' => 'calculation_field_' . time(),
                'formula' => '',
                'display_type' => 'number', // number, currency, percentage
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'total' => array(
                'type' => 'total',
                'label' => __('Total', 'cfb-calculator'),
                'name' => 'total_field_' . time(),
                'formula' => '',
                'display_type' => 'currency',
                'show_breakdown' => true,
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            )
        );
        
        return isset($defaults[$type]) ? $defaults[$type] : array();
    }
    
    /**
     * Save form via AJAX
     */
    public function save_form() {
        $form_data = $_POST['form_data'];
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
        
        // Validate required fields
        if (empty($form_data['name'])) {
            wp_send_json_error(__('Form name is required', 'cfb-calculator'));
        }
        
        // Prepare data for saving
        $save_data = array(
            'name' => sanitize_text_field($form_data['name']),
            'description' => sanitize_textarea_field($form_data['description']),
            'form_data' => $form_data,
            'settings' => isset($form_data['settings']) ? $form_data['settings'] : array(),
            'status' => 'active'
        );
        
        if ($form_id) {
            $save_data['id'] = $form_id;
        }
        
        $saved_id = CFB_Database::get_instance()->save_form($save_data);
        
        if ($saved_id) {
            wp_send_json_success(array(
                'form_id' => $saved_id,
                'message' => __('Form saved successfully', 'cfb-calculator')
            ));
        } else {
            wp_send_json_error(__('Failed to save form', 'cfb-calculator'));
        }
    }
    
    /**
     * Render form builder interface
     */
    public function render_form_builder($form_id = 0) {
        $form = null;
        $form_data = array();

        if ($form_id) {
            $form = CFB_Database::get_instance()->get_form($form_id);
            if ($form) {
                $form_data = json_decode($form->form_data, true);
                if (!$form_data) {
                    $form_data = array();
                }
            }
        }

        $field_types = $this->get_field_types();
        ?>
        <div class="wrap cfb-form-builder">
            <h1><?php echo $form_id ? __('Edit Form', 'cfb-calculator') : __('Create New Form', 'cfb-calculator'); ?></h1>
            
            <div class="cfb-builder-container">
                <!-- Left Sidebar with Tabs -->
                <div class="cfb-left-sidebar">
                    <!-- Tab Navigation -->
                    <div class="cfb-tab-nav">
                        <button type="button" class="cfb-tab-button active" data-tab="settings">
                            <span class="dashicons dashicons-admin-settings"></span>
                            <?php _e('Settings', 'cfb-calculator'); ?>
                        </button>
                        <button type="button" class="cfb-tab-button" data-tab="fields">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Field Types', 'cfb-calculator'); ?>
                        </button>
                    </div>

                    <!-- Settings Tab Content -->
                    <div class="cfb-tab-content active" id="cfb-tab-settings">
                        <div class="cfb-settings-content">
                            <div class="cfb-form-group">
                                <label for="form-name"><?php _e('Form Name', 'cfb-calculator'); ?></label>
                                <input type="text" id="form-name" value="<?php echo $form ? esc_attr($form->name) : ''; ?>" placeholder="<?php _e('Enter form name', 'cfb-calculator'); ?>">
                            </div>

                            <div class="cfb-form-group">
                                <label for="form-description"><?php _e('Description', 'cfb-calculator'); ?></label>
                                <textarea id="form-description" placeholder="<?php _e('Enter form description', 'cfb-calculator'); ?>"><?php echo $form ? esc_textarea($form->description) : ''; ?></textarea>
                            </div>

                            <div class="cfb-form-group">
                                <h4><?php _e('Form Options', 'cfb-calculator'); ?></h4>

                                <label class="cfb-checkbox-label">
                                    <input type="checkbox" id="save-submissions" <?php checked(isset($form_data['save_submissions']) && $form_data['save_submissions']); ?>>
                                    <span class="cfb-checkbox-text"><?php _e('Save Submissions', 'cfb-calculator'); ?></span>
                                </label>

                                <label class="cfb-checkbox-label">
                                    <input type="checkbox" id="auto-calculate" <?php checked(isset($form_data['auto_calculate']) && $form_data['auto_calculate']); ?>>
                                    <span class="cfb-checkbox-text"><?php _e('Auto Calculate', 'cfb-calculator'); ?></span>
                                </label>
                            </div>

                            <div class="cfb-form-group">
                                <h4><?php _e('Display Options', 'cfb-calculator'); ?></h4>

                                <label for="form-theme"><?php _e('Form Theme', 'cfb-calculator'); ?></label>
                                <select id="form-theme">
                                    <option value="default"><?php _e('Default', 'cfb-calculator'); ?></option>
                                    <option value="modern"><?php _e('Modern', 'cfb-calculator'); ?></option>
                                    <option value="minimal"><?php _e('Minimal', 'cfb-calculator'); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Field Types Tab Content -->
                    <div class="cfb-tab-content" id="cfb-tab-fields">
                        <div class="cfb-field-types-content">
                            <div class="cfb-field-types-grid">
                                <?php foreach ($field_types as $type => $config): ?>
                                    <div class="cfb-field-type" data-type="<?php echo esc_attr($type); ?>" title="<?php echo esc_attr($config['label']); ?>">
                                        <span class="dashicons <?php echo esc_attr($config['icon']); ?>"></span>
                                        <span class="cfb-field-type-label"><?php echo esc_html($config['label']); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="cfb-field-types-help">
                                <h4><?php _e('Quick Tips', 'cfb-calculator'); ?></h4>
                                <ul>
                                    <li><?php _e('Click any field type to add it instantly', 'cfb-calculator'); ?></li>
                                    <li><?php _e('Drag fields to reorder them', 'cfb-calculator'); ?></li>
                                    <li><?php _e('Use Calculation fields for intermediate calculations', 'cfb-calculator'); ?></li>
                                    <li><?php _e('Use Total field for final results', 'cfb-calculator'); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Builder Canvas - Now Much Wider -->
                <div class="cfb-builder-canvas">
                    <div class="cfb-canvas-header">
                        <h3><?php _e('Form Builder', 'cfb-calculator'); ?></h3>
                        <div class="cfb-canvas-actions">
                            <button type="button" class="button" id="preview-form">
                                <span class="dashicons dashicons-visibility"></span>
                                <?php _e('Preview', 'cfb-calculator'); ?>
                            </button>
                            <button type="button" class="button-primary" id="save-form">
                                <span class="dashicons dashicons-saved"></span>
                                <?php _e('Save Form', 'cfb-calculator'); ?>
                            </button>
                        </div>
                    </div>

                    <div class="cfb-form-fields" id="cfb-form-fields">
                        <?php if (isset($form_data['fields']) && is_array($form_data['fields'])): ?>
                            <?php foreach ($form_data['fields'] as $field): ?>
                                <?php $this->render_field_editor($field); ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <div class="cfb-drop-zone">
                        <div class="cfb-drop-zone-content">
                            <span class="dashicons dashicons-plus-alt2"></span>
                            <h4><?php _e('Add Fields to Your Form', 'cfb-calculator'); ?></h4>
                            <p><?php _e('Click field types from the sidebar or drag them here to build your form', 'cfb-calculator'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons moved to canvas header -->
            <div class="cfb-form-actions">
                <a href="<?php echo admin_url('admin.php?page=cfb-calculator'); ?>" class="button"><?php _e('← Back to Forms', 'cfb-calculator'); ?></a>
            </div>
            
            <input type="hidden" id="form-id" value="<?php echo $form_id; ?>">

            <!-- Form Data for JavaScript -->
            <script type="text/javascript">
                window.cfbFormData = <?php echo wp_json_encode($form_data); ?>;
                window.cfbFormInfo = <?php echo wp_json_encode($form ? array(
                    'id' => $form->id,
                    'name' => $form->name,
                    'description' => $form->description,
                    'status' => $form->status
                ) : array()); ?>;
            </script>
        </div>
        
        <!-- Field Editor Template -->
        <script type="text/template" id="cfb-field-editor-template">
            <div class="cfb-field-editor" data-field-type="{{type}}">
                <div class="cfb-field-header">
                    <span class="cfb-field-title">{{label}}</span>
                    <div class="cfb-field-actions">
                        <button type="button" class="cfb-edit-field" title="<?php _e('Edit Field', 'cfb-calculator'); ?>">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="cfb-delete-field" title="<?php _e('Delete Field', 'cfb-calculator'); ?>">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
                <div class="cfb-field-preview">
                    <!-- Field preview will be rendered here -->
                </div>
                <div class="cfb-field-settings" style="display: none;">
                    <!-- Field settings will be rendered here -->
                </div>
            </div>
        </script>
        <?php
    }
    
    /**
     * Render field editor for existing field
     */
    private function render_field_editor($field) {
        // This would render the field editor HTML
        // Implementation would be in JavaScript for dynamic editing
    }
    
    /**
     * Render subtotal editor
     */
    private function render_subtotal_editor($subtotal, $index) {
        ?>
        <div class="cfb-subtotal-editor" data-index="<?php echo $index; ?>">
            <div class="cfb-form-group">
                <label><?php _e('Subtotal Label', 'cfb-calculator'); ?></label>
                <input type="text" class="subtotal-label" value="<?php echo esc_attr($subtotal['label']); ?>" placeholder="<?php _e('Enter subtotal label', 'cfb-calculator'); ?>">
            </div>
            <div class="cfb-form-group">
                <label><?php _e('Formula', 'cfb-calculator'); ?></label>
                <textarea class="subtotal-formula" placeholder="<?php _e('Enter formula', 'cfb-calculator'); ?>"><?php echo esc_textarea($subtotal['formula']); ?></textarea>
            </div>
            <button type="button" class="button cfb-remove-subtotal"><?php _e('Remove', 'cfb-calculator'); ?></button>
        </div>
        <?php
    }

    /**
     * Render formula variables for formula builder
     */
    private function render_formula_variables() {
        if (!class_exists('CFB_Database')) {
            echo '<p class="cfb-no-items">' . __('Variables not available', 'cfb-calculator') . '</p>';
            return;
        }

        $variables = CFB_Database::get_instance()->get_variables();

        if (empty($variables)) {
            echo '<p class="cfb-no-items">' . __('No variables created yet. ', 'cfb-calculator') .
                 '<a href="' . admin_url('admin.php?page=cfb-calculator-variables') . '">' .
                 __('Create variables', 'cfb-calculator') . '</a></p>';
            return;
        }

        foreach ($variables as $variable) {
            ?>
            <div class="cfb-formula-item cfb-variable-item" data-insert="{<?php echo esc_attr($variable->name); ?>}" title="<?php echo esc_attr($variable->description); ?>">
                <span class="cfb-item-icon dashicons <?php echo esc_attr($variable->icon); ?>" style="color: <?php echo esc_attr($variable->color); ?>"></span>
                <div class="cfb-item-content">
                    <span class="cfb-item-name"><?php echo esc_html($variable->label); ?></span>
                    <span class="cfb-item-code">{<?php echo esc_html($variable->name); ?>}</span>
                    <span class="cfb-item-value"><?php echo number_format($variable->value, 2); ?></span>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render formula functions for formula builder
     */
    private function render_formula_functions() {
        $functions = array(
            'SUM' => array(
                'label' => __('Sum', 'cfb-calculator'),
                'syntax' => 'SUM(field1, field2, ...)',
                'description' => __('Add multiple values together', 'cfb-calculator'),
                'icon' => 'dashicons-plus-alt'
            ),
            'MIN' => array(
                'label' => __('Minimum', 'cfb-calculator'),
                'syntax' => 'MIN(field1, field2, ...)',
                'description' => __('Get the smallest value', 'cfb-calculator'),
                'icon' => 'dashicons-arrow-down-alt'
            ),
            'MAX' => array(
                'label' => __('Maximum', 'cfb-calculator'),
                'syntax' => 'MAX(field1, field2, ...)',
                'description' => __('Get the largest value', 'cfb-calculator'),
                'icon' => 'dashicons-arrow-up-alt'
            ),
            'ROUND' => array(
                'label' => __('Round', 'cfb-calculator'),
                'syntax' => 'ROUND(value, decimals)',
                'description' => __('Round to specified decimal places', 'cfb-calculator'),
                'icon' => 'dashicons-marker'
            ),
            'IF' => array(
                'label' => __('If Condition', 'cfb-calculator'),
                'syntax' => 'condition ? value_if_true : value_if_false',
                'description' => __('Conditional logic', 'cfb-calculator'),
                'icon' => 'dashicons-randomize'
            ),
            'ABS' => array(
                'label' => __('Absolute', 'cfb-calculator'),
                'syntax' => 'ABS(value)',
                'description' => __('Get absolute value', 'cfb-calculator'),
                'icon' => 'dashicons-editor-expand'
            )
        );

        foreach ($functions as $func => $config) {
            ?>
            <div class="cfb-formula-item cfb-function-item" data-insert="<?php echo esc_attr($config['syntax']); ?>" title="<?php echo esc_attr($config['description']); ?>">
                <span class="cfb-item-icon dashicons <?php echo esc_attr($config['icon']); ?>"></span>
                <div class="cfb-item-content">
                    <span class="cfb-item-name"><?php echo esc_html($config['label']); ?></span>
                    <span class="cfb-item-code"><?php echo esc_html($func); ?></span>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render formula operators for formula builder
     */
    private function render_formula_operators() {
        $operators = array(
            '+' => array('label' => __('Add', 'cfb-calculator'), 'icon' => 'dashicons-plus-alt'),
            '-' => array('label' => __('Subtract', 'cfb-calculator'), 'icon' => 'dashicons-minus'),
            '*' => array('label' => __('Multiply', 'cfb-calculator'), 'icon' => 'dashicons-dismiss'),
            '/' => array('label' => __('Divide', 'cfb-calculator'), 'icon' => 'dashicons-editor-quote'),
            '%' => array('label' => __('Modulo', 'cfb-calculator'), 'icon' => 'dashicons-admin-generic'),
            '(' => array('label' => __('Open Parenthesis', 'cfb-calculator'), 'icon' => 'dashicons-editor-paragraph'),
            ')' => array('label' => __('Close Parenthesis', 'cfb-calculator'), 'icon' => 'dashicons-editor-paragraph'),
            '>' => array('label' => __('Greater Than', 'cfb-calculator'), 'icon' => 'dashicons-arrow-right-alt'),
            '<' => array('label' => __('Less Than', 'cfb-calculator'), 'icon' => 'dashicons-arrow-left-alt'),
            '==' => array('label' => __('Equal To', 'cfb-calculator'), 'icon' => 'dashicons-yes'),
            '!=' => array('label' => __('Not Equal To', 'cfb-calculator'), 'icon' => 'dashicons-no'),
            '>=' => array('label' => __('Greater or Equal', 'cfb-calculator'), 'icon' => 'dashicons-arrow-right-alt2'),
            '<=' => array('label' => __('Less or Equal', 'cfb-calculator'), 'icon' => 'dashicons-arrow-left-alt2')
        );

        foreach ($operators as $op => $config) {
            ?>
            <div class="cfb-formula-item cfb-operator-item" data-insert=" <?php echo esc_attr($op); ?> " title="<?php echo esc_attr($config['label']); ?>">
                <span class="cfb-item-icon dashicons <?php echo esc_attr($config['icon']); ?>"></span>
                <div class="cfb-item-content">
                    <span class="cfb-item-name"><?php echo esc_html($config['label']); ?></span>
                    <span class="cfb-item-code"><?php echo esc_html($op); ?></span>
                </div>
            </div>
            <?php
        }
    }
}
