<?php
/**
 * CFB Calculator Dashboard
 * Handles dashboard statistics and analytics
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Dashboard {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Constructor
    }
    
    /**
     * Render dashboard page
     */
    public function render_dashboard() {
        $stats = $this->get_dashboard_stats();
        $recent_activity = $this->get_recent_activity();
        $popular_forms = $this->get_popular_forms();
        ?>
        <div class="wrap cfb-dashboard">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-dashboard"></span>
                <?php _e('CFB Calculator Dashboard', 'cfb-calculator'); ?>
            </h1>
            
            <!-- Statistics Cards -->
            <div class="cfb-stats-grid">
                <div class="cfb-stat-card">
                    <div class="cfb-stat-icon">
                        <span class="dashicons dashicons-forms"></span>
                    </div>
                    <div class="cfb-stat-content">
                        <h3><?php echo number_format($stats['total_forms']); ?></h3>
                        <p><?php _e('Total Forms', 'cfb-calculator'); ?></p>
                    </div>
                </div>
                
                <div class="cfb-stat-card">
                    <div class="cfb-stat-icon">
                        <span class="dashicons dashicons-chart-line"></span>
                    </div>
                    <div class="cfb-stat-content">
                        <h3><?php echo number_format($stats['total_submissions']); ?></h3>
                        <p><?php _e('Total Submissions', 'cfb-calculator'); ?></p>
                    </div>
                </div>
                
                <div class="cfb-stat-card">
                    <div class="cfb-stat-icon">
                        <span class="dashicons dashicons-money-alt"></span>
                    </div>
                    <div class="cfb-stat-content">
                        <h3><?php echo $this->format_currency($stats['total_revenue']); ?></h3>
                        <p><?php _e('Total Revenue', 'cfb-calculator'); ?></p>
                    </div>
                </div>
                
                <div class="cfb-stat-card">
                    <div class="cfb-stat-icon">
                        <span class="dashicons dashicons-media-document"></span>
                    </div>
                    <div class="cfb-stat-content">
                        <h3><?php echo number_format($stats['total_invoices']); ?></h3>
                        <p><?php _e('Total Invoices', 'cfb-calculator'); ?></p>
                    </div>
                </div>
            </div>
            
            <!-- Charts and Analytics -->
            <div class="cfb-dashboard-row">
                <div class="cfb-dashboard-col-8">
                    <div class="cfb-dashboard-widget">
                        <h2><?php _e('Submissions Over Time', 'cfb-calculator'); ?></h2>
                        <div class="cfb-chart-container">
                            <canvas id="cfb-submissions-chart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="cfb-dashboard-col-4">
                    <div class="cfb-dashboard-widget">
                        <h2><?php _e('Popular Forms', 'cfb-calculator'); ?></h2>
                        <div class="cfb-popular-forms">
                            <?php foreach ($popular_forms as $form): ?>
                                <div class="cfb-popular-form-item">
                                    <div class="cfb-form-info">
                                        <h4><?php echo esc_html($form->name); ?></h4>
                                        <span class="cfb-submission-count">
                                            <?php printf(__('%d submissions', 'cfb-calculator'), $form->submission_count); ?>
                                        </span>
                                    </div>
                                    <div class="cfb-form-actions">
                                        <a href="<?php echo admin_url('admin.php?page=cfb-calculator-new&form_id=' . $form->id); ?>" 
                                           class="button button-small"><?php _e('Edit', 'cfb-calculator'); ?></a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="cfb-dashboard-widget">
                <h2><?php _e('Recent Activity', 'cfb-calculator'); ?></h2>
                <div class="cfb-recent-activity">
                    <?php if (empty($recent_activity)): ?>
                        <p class="cfb-no-activity"><?php _e('No recent activity found.', 'cfb-calculator'); ?></p>
                    <?php else: ?>
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th><?php _e('Form', 'cfb-calculator'); ?></th>
                                    <th><?php _e('Total', 'cfb-calculator'); ?></th>
                                    <th><?php _e('Date', 'cfb-calculator'); ?></th>
                                    <th><?php _e('Actions', 'cfb-calculator'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_activity as $activity): ?>
                                    <tr>
                                        <td><?php echo esc_html($activity->form_name); ?></td>
                                        <td><?php echo $this->format_currency($activity->calculated_total); ?></td>
                                        <td><?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($activity->submitted_at)); ?></td>
                                        <td>
                                            <button class="button button-small cfb-create-invoice" 
                                                    data-submission-id="<?php echo $activity->id; ?>"
                                                    data-form-id="<?php echo $activity->form_id; ?>"
                                                    data-total="<?php echo $activity->calculated_total; ?>">
                                                <?php _e('Create Invoice', 'cfb-calculator'); ?>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="cfb-quick-actions">
                <h2><?php _e('Quick Actions', 'cfb-calculator'); ?></h2>
                <div class="cfb-action-buttons">
                    <a href="<?php echo admin_url('admin.php?page=cfb-calculator-new'); ?>" class="button button-primary button-hero">
                        <span class="dashicons dashicons-plus-alt"></span>
                        <?php _e('Create New Form', 'cfb-calculator'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=cfb-calculator-invoices'); ?>" class="button button-secondary button-hero">
                        <span class="dashicons dashicons-media-document"></span>
                        <?php _e('Manage Invoices', 'cfb-calculator'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=cfb-calculator-variables'); ?>" class="button button-secondary button-hero">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php _e('Manage Variables', 'cfb-calculator'); ?>
                    </a>
                </div>
            </div>
        </div>
        
        <style>
        .cfb-dashboard {
            margin: 20px 0;
        }
        
        .cfb-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .cfb-stat-card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            transition: box-shadow 0.2s;
        }
        
        .cfb-stat-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .cfb-stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cfb-stat-icon .dashicons {
            color: white;
            font-size: 24px;
        }
        
        .cfb-stat-content h3 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            color: #23282d;
        }
        
        .cfb-stat-content p {
            margin: 5px 0 0 0;
            color: #646970;
            font-size: 14px;
        }
        
        .cfb-dashboard-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .cfb-dashboard-widget {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
        }
        
        .cfb-dashboard-widget h2 {
            margin: 0 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .cfb-chart-container {
            height: 300px;
            position: relative;
        }
        
        .cfb-popular-form-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .cfb-popular-form-item:last-child {
            border-bottom: none;
        }
        
        .cfb-form-info h4 {
            margin: 0 0 5px 0;
            font-size: 14px;
        }
        
        .cfb-submission-count {
            font-size: 12px;
            color: #646970;
        }
        
        .cfb-quick-actions {
            margin: 30px 0;
        }
        
        .cfb-action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .cfb-action-buttons .button-hero {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .cfb-no-activity {
            text-align: center;
            color: #646970;
            font-style: italic;
            padding: 40px 20px;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Chart.js implementation would go here
            // For now, we'll add a placeholder
            
            // Handle create invoice buttons
            $('.cfb-create-invoice').on('click', function() {
                const submissionId = $(this).data('submission-id');
                const formId = $(this).data('form-id');
                const total = $(this).data('total');
                
                // Redirect to invoice creation page with parameters
                window.location.href = '<?php echo admin_url('admin.php?page=cfb-calculator-invoices&action=create'); ?>' + 
                                     '&submission_id=' + submissionId + 
                                     '&form_id=' + formId + 
                                     '&total=' + total;
            });
        });
        </script>
        <?php
    }

    /**
     * Get dashboard statistics
     */
    private function get_dashboard_stats() {
        global $wpdb;

        $stats = array();

        // Total forms
        $stats['total_forms'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}cfb_forms");

        // Total submissions
        $stats['total_submissions'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}cfb_form_submissions");

        // Total revenue (sum of all calculated totals)
        $stats['total_revenue'] = $wpdb->get_var("SELECT SUM(calculated_total) FROM {$wpdb->prefix}cfb_form_submissions") ?: 0;

        // Total invoices
        $stats['total_invoices'] = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}cfb_invoices") ?: 0;

        return $stats;
    }

    /**
     * Get recent activity
     */
    private function get_recent_activity($limit = 10) {
        global $wpdb;

        return $wpdb->get_results($wpdb->prepare("
            SELECT s.*, f.name as form_name
            FROM {$wpdb->prefix}cfb_form_submissions s
            LEFT JOIN {$wpdb->prefix}cfb_forms f ON s.form_id = f.id
            ORDER BY s.submitted_at DESC
            LIMIT %d
        ", $limit));
    }

    /**
     * Get popular forms
     */
    private function get_popular_forms($limit = 5) {
        global $wpdb;

        return $wpdb->get_results($wpdb->prepare("
            SELECT f.*, COUNT(s.id) as submission_count
            FROM {$wpdb->prefix}cfb_forms f
            LEFT JOIN {$wpdb->prefix}cfb_form_submissions s ON f.id = s.form_id
            WHERE f.status = 'active'
            GROUP BY f.id
            ORDER BY submission_count DESC
            LIMIT %d
        ", $limit));
    }

    /**
     * Format currency for display
     */
    private function format_currency($amount) {
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $currency_position = get_option('cfb_currency_position', 'left');
        $decimal_places = get_option('cfb_decimal_places', 2);

        $formatted = number_format($amount, $decimal_places);

        if ($currency_position === 'right') {
            return $formatted . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted;
        }
    }
}
