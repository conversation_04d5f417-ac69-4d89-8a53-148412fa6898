<?php
/**
 * CFB Calculator Formula Engine
 * Handles complex formula calculations with support for mathematical functions
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Formula_Engine {
    
    private static $instance = null;
    private $variables = array();
    private $functions = array();
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_functions();
    }
    
    /**
     * Initialize supported functions
     */
    private function init_functions() {
        $this->functions = array(
            'ceil' => 'ceil',
            'floor' => 'floor',
            'min' => 'min',
            'max' => 'max',
            'pow' => 'pow',
            'sqrt' => 'sqrt',
            'abs' => 'abs',
            'round' => 'round',
            'if' => array($this, 'if_function'),
            'ifelse' => array($this, 'ifelse_function')
        );
    }
    
    /**
     * AJAX handler for price calculation
     */
    public function calculate_price() {
        try {
            // Log the incoming request for debugging
            error_log('CFB Calculate Price Request: ' . print_r($_POST, true));

            // Verify nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'cfb_calculator_nonce')) {
                error_log('CFB Nonce verification failed. Expected: cfb_calculator_nonce, Received: ' . (isset($_POST['nonce']) ? $_POST['nonce'] : 'none'));
                wp_send_json_error(__('Security check failed', 'cfb-calculator'));
                return;
            }

            $form_id = intval($_POST['form_id']);
            $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : array();

            error_log('CFB Processing form ID: ' . $form_id . ', Form data: ' . print_r($form_data, true));

            if (!$form_id) {
                error_log('CFB Invalid form ID provided');
                wp_send_json_error(__('Invalid form ID', 'cfb-calculator'));
                return;
            }
        
            // Get form configuration
            $form = CFB_Database::get_instance()->get_form($form_id);
            if (!$form) {
                wp_send_json_error(__('Form not found', 'cfb-calculator'));
                return;
            }

            $form_config = json_decode($form->form_data, true);
            if (!$form_config) {
                error_log('CFB Failed to decode form configuration: ' . $form->form_data);
                wp_send_json_error(__('Invalid form configuration', 'cfb-calculator'));
                return;
            }

            // Ensure form has fields
            if (!isset($form_config['fields']) || !is_array($form_config['fields'])) {
                error_log('CFB Form has no fields or invalid fields structure');
                wp_send_json_error(__('Form has no fields configured', 'cfb-calculator'));
                return;
            }

            error_log('CFB Form config loaded successfully with ' . count($form_config['fields']) . ' fields');

            // Get settings with defaults
            $settings = array(
                'currency_symbol' => get_option('cfb_currency_symbol', '$'),
                'currency_position' => get_option('cfb_currency_position', 'left'),
                'decimal_places' => get_option('cfb_decimal_places', 2),
                'decimal_separator' => get_option('cfb_decimal_separator', '.'),
                'thousand_separator' => get_option('cfb_thousand_separator', ',')
            );

            // Process form data and calculate
            $result = $this->process_calculation($form_data, $form_config, $settings);

            // Save submission if enabled
            if (isset($form_config['save_submissions']) && $form_config['save_submissions']) {
                CFB_Database::get_instance()->save_submission($form_id, $form_data, $result['total']);
            }

            wp_send_json_success($result);

        } catch (Exception $e) {
            error_log('CFB Calculation Error: ' . $e->getMessage() . ' Stack: ' . $e->getTraceAsString());
            wp_send_json_error('Calculation error: ' . $e->getMessage());
        } catch (Error $e) {
            error_log('CFB Fatal Error: ' . $e->getMessage() . ' Stack: ' . $e->getTraceAsString());
            wp_send_json_error('Fatal error: ' . $e->getMessage());
        }
    }
    
    /**
     * Process calculation based on form data and configuration
     */
    public function process_calculation($form_data, $form_config, $settings) {
        try {
            $this->variables = array();
            $calculations = array();
            $total = 0;

            $field_count = isset($form_config['fields']) && is_array($form_config['fields']) ? count($form_config['fields']) : 0;
            error_log('CFB Starting calculation process with ' . $field_count . ' fields');

            // Load global variables first
            $this->load_global_variables();

            // First pass: Process basic fields (non-calculation fields)
            if (isset($form_config['fields']) && is_array($form_config['fields'])) {
                foreach ($form_config['fields'] as $field) {
                if (!isset($field['type']) || !isset($field['name'])) {
                    error_log('CFB Skipping invalid field: ' . print_r($field, true));
                    continue;
                }

                if (in_array($field['type'], array('calculation', 'total'))) {
                    continue; // Skip calculation fields for now
                }

                $field_name = $field['name'];
                $field_value = isset($form_data[$field_name]) ? $form_data[$field_name] : '';

                error_log("CFB Processing field: {$field_name} = {$field_value} (type: {$field['type']})");

                // Check conditional logic - if field is hidden, set value to 0 and skip
                if (!$this->check_conditional_logic($field, $form_data)) {
                    error_log("CFB Field {$field_name} hidden by conditional logic - setting to 0");
                    $this->variables[$field_name] = 0;
                    continue;
                }

                // Process field value based on type
                $processed_value = $this->process_field_value($field, $field_value);
                $this->variables[$field_name] = $processed_value;

                    error_log("CFB Field {$field_name} processed value: {$processed_value}");
                }
            }

            // Second pass: Process calculation fields
            if (isset($form_config['fields']) && is_array($form_config['fields'])) {
                foreach ($form_config['fields'] as $field) {
                if (!in_array($field['type'], array('calculation', 'total'))) {
                    continue; // Skip non-calculation fields
                }

                $field_name = $field['name'];

                error_log("CFB Processing calculation field: {$field_name} (type: {$field['type']})");

                // Check conditional logic - if calculation field is hidden, set to 0 and skip
                if (!$this->check_conditional_logic($field, $form_data)) {
                    error_log("CFB Calculation field {$field_name} hidden by conditional logic - setting to 0");
                    $this->variables[$field_name] = 0;

                    // Still add to calculations array but with 0 value
                    $calculations[] = array(
                        'name' => $field_name,
                        'label' => $field['label'],
                        'type' => $field['type'],
                        'value' => 0,
                        'display_type' => isset($field['display_type']) ? $field['display_type'] : 'number',
                        'formatted' => $this->format_value(0, $field, $settings),
                        'hidden' => true
                    );
                    continue;
                }

                // Calculate value using formula
                $calculated_value = 0;
                if (isset($field['formula']) && !empty($field['formula'])) {
                    error_log("CFB Evaluating formula for {$field_name}: {$field['formula']}");
                    $calculated_value = $this->evaluate_formula($field['formula']);
                    error_log("CFB Formula result for {$field_name}: {$calculated_value}");
                }

                $this->variables[$field_name] = $calculated_value;

                $calculations[] = array(
                    'name' => $field_name,
                    'label' => $field['label'],
                    'type' => $field['type'],
                    'value' => $calculated_value,
                    'display_type' => isset($field['display_type']) ? $field['display_type'] : 'number',
                    'formatted' => $this->format_value($calculated_value, $field, $settings)
                );

                    // If this is a total field, use it as the main total
                    if ($field['type'] === 'total') {
                        $total = $calculated_value;
                    }
                }
            }

            // If no total field found, sum all calculation fields
            if ($total === 0 && !empty($calculations)) {
                $total = array_sum(array_column($calculations, 'value'));
            }

            error_log('CFB Calculation completed. Total: ' . $total . ', Calculations: ' . print_r($calculations, true));

            return array(
                'calculations' => $calculations,
                'total' => $total,
                'formatted_total' => $this->format_currency($total, $settings),
                'variables' => $this->variables
            );

        } catch (Exception $e) {
            error_log('CFB Process calculation error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Load global variables into the variables array
     */
    private function load_global_variables() {
        try {
            $global_variables = CFB_Database::get_instance()->get_variables();

            if (empty($global_variables)) {
                error_log("CFB: No global variables found in database");
                return;
            }

            foreach ($global_variables as $variable) {
                $this->variables[$variable->name] = floatval($variable->value);
                error_log("CFB Loaded global variable: {$variable->name} = {$variable->value}");
            }

            error_log("CFB: Total global variables loaded: " . count($global_variables));
        } catch (Exception $e) {
            error_log("CFB: Error loading global variables: " . $e->getMessage());
        }
    }

    /**
     * Process field value based on field type
     */
    private function process_field_value($field, $value) {
        switch ($field['type']) {
            case 'number':
            case 'slider':
                return floatval($value);
                
            case 'dropdown':
            case 'radio':
                // Return the selected value directly (not price)
                return is_numeric($value) ? floatval($value) : 0;
                
            case 'checkbox':
                $total = 0;
                if (is_array($value)) {
                    foreach ($value as $selected_value) {
                        // Add the numeric value of each selected checkbox
                        $total += is_numeric($selected_value) ? floatval($selected_value) : 0;
                    }
                }
                return $total;
                
            case 'text':
                // For text fields, check if there's a calculation rule
                if (isset($field['calculation_rule'])) {
                    return $this->apply_text_calculation_rule($value, $field['calculation_rule']);
                }
                return 0;
                
            default:
                return 0;
        }
    }
    
    /**
     * Apply calculation rule for text fields
     */
    private function apply_text_calculation_rule($value, $rule) {
        switch ($rule['type']) {
            case 'per_character':
                return strlen($value) * floatval($rule['rate']);
                
            case 'per_word':
                return str_word_count($value) * floatval($rule['rate']);
                
            case 'fixed_rate':
                return !empty($value) ? floatval($rule['rate']) : 0;
                
            default:
                return 0;
        }
    }
    
    /**
     * Check conditional logic for a field
     */
    private function check_conditional_logic($field, $form_data) {
        if (!isset($field['conditional_logic']) || !$field['conditional_logic']['enabled']) {
            return true;
        }

        $logic = $field['conditional_logic'];

        if (!isset($logic['conditions']) || !is_array($logic['conditions'])) {
            return true;
        }

        $conditions_met = 0;

        foreach ($logic['conditions'] as $condition) {
            $field_value = isset($form_data[$condition['field']]) ? $form_data[$condition['field']] : '';
            $condition_met = false;
            
            switch ($condition['operator']) {
                case 'equals':
                    $condition_met = ($field_value == $condition['value']);
                    break;
                case 'not_equals':
                    $condition_met = ($field_value != $condition['value']);
                    break;
                case 'greater_than':
                    $condition_met = (floatval($field_value) > floatval($condition['value']));
                    break;
                case 'less_than':
                    $condition_met = (floatval($field_value) < floatval($condition['value']));
                    break;
                case 'contains':
                    $condition_met = (strpos($field_value, $condition['value']) !== false);
                    break;
                case 'not_empty':
                    $condition_met = !empty($field_value);
                    break;
                case 'empty':
                    $condition_met = empty($field_value);
                    break;
            }
            
            if ($condition_met) {
                $conditions_met++;
            }
        }
        
        // Check if conditions are met based on logic type
        $total_conditions = is_array($logic['conditions']) ? count($logic['conditions']) : 0;

        if ($logic['logic_type'] === 'all') {
            return $conditions_met === $total_conditions;
        } else {
            return $conditions_met > 0;
        }
    }
    
    /**
     * Evaluate mathematical formula
     */
    public function evaluate_formula($formula) {
        if (empty($formula)) {
            return 0;
        }

        error_log("CFB: Starting formula evaluation: {$formula}");

        // Replace variables with their values
        $after_variables = $this->replace_variables($formula);
        error_log("CFB: After variable replacement: {$after_variables}");

        // Replace function calls
        $after_functions = $this->replace_functions($after_variables);
        error_log("CFB: After function replacement: {$after_functions}");

        // Evaluate the mathematical expression safely
        $result = $this->safe_eval($after_functions);
        error_log("CFB: Final result: {$result}");

        return $result;
    }
    
    /**
     * Replace variables in formula with their values
     */
    private function replace_variables($formula) {
        foreach ($this->variables as $var_name => $var_value) {
            $formula = str_replace('{' . $var_name . '}', $var_value, $formula);
        }
        return $formula;
    }
    
    /**
     * Replace function calls in formula
     */
    private function replace_functions($formula) {
        $max_iterations = 50;
        $iteration = 0;

        error_log("CFB: Starting function replacement for: {$formula}");

        while ($iteration < $max_iterations) {
            $original_formula = $formula;
            $found_function = false;

            // Find all function calls and process the innermost one
            if (preg_match_all('/(\w+)\s*\(/', $formula, $matches, PREG_OFFSET_CAPTURE)) {
                // Process from right to left to handle innermost functions first
                for ($i = count($matches[0]) - 1; $i >= 0; $i--) {
                    $function_name = strtolower($matches[1][$i][0]);
                    $start_pos = $matches[0][$i][1];

                    // Find the matching closing parenthesis
                    $paren_count = 0;
                    $end_pos = -1;
                    $func_start = $start_pos + strlen($matches[0][$i][0]) - 1; // Position of opening parenthesis

                    for ($j = $func_start; $j < strlen($formula); $j++) {
                        if ($formula[$j] === '(') {
                            $paren_count++;
                        } elseif ($formula[$j] === ')') {
                            $paren_count--;
                            if ($paren_count === 0) {
                                $end_pos = $j;
                                break;
                            }
                        }
                    }

                    if ($end_pos !== -1) {
                        $full_function = substr($formula, $start_pos, $end_pos - $start_pos + 1);
                        $params_start = strpos($full_function, '(') + 1;
                        $params = substr($full_function, $params_start, -1);

                        error_log("CFB: Found function: {$function_name}({$params})");

                        if (isset($this->functions[$function_name])) {
                            $result = $this->call_function($function_name, $params);
                            $formula = substr_replace($formula, $result, $start_pos, strlen($full_function));
                            error_log("CFB: Replaced {$full_function} with {$result}, formula now: {$formula}");
                            $found_function = true;
                            break; // Process one function at a time
                        } else {
                            error_log("CFB: Unknown function: {$function_name}");
                            $formula = substr_replace($formula, '0', $start_pos, strlen($full_function));
                            $found_function = true;
                            break;
                        }
                    }
                }
            }

            if (!$found_function) {
                // No more functions found
                break;
            }

            // Safety check to prevent infinite loops
            if ($formula === $original_formula) {
                error_log("CFB: Formula unchanged, breaking loop");
                break;
            }

            $iteration++;
        }

        if ($iteration >= $max_iterations) {
            error_log("CFB: Maximum function replacement iterations reached");
        }

        error_log("CFB: Function replacement completed: {$formula}");
        return $formula;
    }
    
    /**
     * Call a function with parameters
     */
    private function call_function($function_name, $params_string) {
        try {
            // Handle empty parameters
            if (empty(trim($params_string))) {
                $params = array();
            } else {
                $params = array_map('trim', explode(',', $params_string));
            }

            // Evaluate each parameter
            if (is_array($params)) {
                for ($i = 0; $i < count($params); $i++) {
                    // Simply evaluate the parameter as a mathematical expression
                    $params[$i] = $this->safe_eval($params[$i]);
                    error_log("CFB: Function {$function_name} param {$i}: {$params[$i]}");
                }
            }

            $function = $this->functions[$function_name];

            if (is_callable($function)) {
                $result = call_user_func_array($function, $params);
                error_log("CFB: Function {$function_name} called with result: {$result}");
                return $result;
            }

            error_log("CFB: Function {$function_name} is not callable");
            return 0;

        } catch (Exception $e) {
            error_log("CFB: Error calling function {$function_name}: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * IF function implementation
     */
    public function if_function($condition, $true_value, $false_value = 0) {
        return $condition ? $true_value : $false_value;
    }
    
    /**
     * IFELSE function implementation (alias for IF)
     */
    public function ifelse_function($condition, $true_value, $false_value = 0) {
        return $this->if_function($condition, $true_value, $false_value);
    }
    
    /**
     * Safely evaluate mathematical expression without using eval()
     */
    private function safe_eval($expression) {
        if (empty($expression)) {
            return 0;
        }

        // Clean the expression
        $expression = trim($expression);

        // Handle comparison operators first (for conditional logic)
        if (preg_match('/(.+?)\s*(>=|<=|>|<|==|!=)\s*(.+)/', $expression, $matches)) {
            $left = $this->safe_eval(trim($matches[1]));
            $operator = trim($matches[2]);
            $right = $this->safe_eval(trim($matches[3]));

            switch ($operator) {
                case '>=': return $left >= $right ? 1 : 0;
                case '<=': return $left <= $right ? 1 : 0;
                case '>': return $left > $right ? 1 : 0;
                case '<': return $left < $right ? 1 : 0;
                case '==': return $left == $right ? 1 : 0;
                case '!=': return $left != $right ? 1 : 0;
            }
        }

        // If it's just a number, return it
        if (is_numeric($expression)) {
            return floatval($expression);
        }

        // Use the mathematical expression parser
        return $this->parse_mathematical_expression($expression);
    }

    /**
     * Parse and evaluate mathematical expressions safely
     */
    private function parse_mathematical_expression($expression) {
        try {
            // Clean the expression but preserve structure
            $expression = trim($expression);

            // Remove extra spaces but keep the expression readable for debugging
            $expression = preg_replace('/\s+/', '', $expression);

            // More permissive validation - allow numbers, operators, parentheses, decimal points, and spaces
            // This regex allows: digits, +, -, *, /, (, ), decimal points, and spaces
            if (!preg_match('/^[0-9+\-*\/\(\)\.\s]+$/', $expression)) {
                error_log('CFB: Invalid characters in expression: ' . $expression);
                return 0;
            }

            // Check for balanced parentheses
            if (substr_count($expression, '(') !== substr_count($expression, ')')) {
                error_log('CFB: Unbalanced parentheses in expression: ' . $expression);
                return 0;
            }

            // Check for empty expression
            if (empty($expression)) {
                return 0;
            }

            // Parse the expression using recursive descent parser
            $tokens = $this->tokenize($expression);
            if (empty($tokens)) {
                error_log('CFB: No tokens generated from expression: ' . $expression);
                return 0;
            }

            $index = 0;
            $result = $this->parse_expression($tokens, $index);

            return is_numeric($result) ? floatval($result) : 0;

        } catch (Exception $e) {
            error_log('CFB: Error parsing expression "' . $expression . '": ' . $e->getMessage());
            return 0;
        } catch (Error $e) {
            error_log('CFB: Fatal error parsing expression "' . $expression . '": ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Tokenize mathematical expression
     */
    private function tokenize($expression) {
        $tokens = array();
        $current_number = '';

        for ($i = 0; $i < strlen($expression); $i++) {
            $char = $expression[$i];

            if (is_numeric($char) || $char === '.') {
                $current_number .= $char;
            } else {
                if ($current_number !== '') {
                    $tokens[] = floatval($current_number);
                    $current_number = '';
                }

                if (in_array($char, array('+', '-', '*', '/', '(', ')'))) {
                    $tokens[] = $char;
                }
            }
        }

        if ($current_number !== '') {
            $tokens[] = floatval($current_number);
        }

        return $tokens;
    }

    /**
     * Parse expression using recursive descent parser
     */
    private function parse_expression($tokens, &$index) {
        $result = $this->parse_term($tokens, $index);

        while ($index < count($tokens) && in_array($tokens[$index], array('+', '-'))) {
            $operator = $tokens[$index++];
            $right = $this->parse_term($tokens, $index);

            if ($operator === '+') {
                $result += $right;
            } else {
                $result -= $right;
            }
        }

        return $result;
    }

    /**
     * Parse term (multiplication and division)
     */
    private function parse_term($tokens, &$index) {
        $result = $this->parse_factor($tokens, $index);

        while ($index < count($tokens) && in_array($tokens[$index], array('*', '/'))) {
            $operator = $tokens[$index++];
            $right = $this->parse_factor($tokens, $index);

            if ($operator === '*') {
                $result *= $right;
            } else {
                if ($right != 0) {
                    $result /= $right;
                } else {
                    error_log('CFB: Division by zero in expression');
                    return 0;
                }
            }
        }

        return $result;
    }

    /**
     * Parse factor (numbers and parentheses)
     */
    private function parse_factor($tokens, &$index) {
        if ($index >= count($tokens)) {
            return 0;
        }

        $token = $tokens[$index];

        if (is_numeric($token)) {
            $index++;
            return $token;
        }

        if ($token === '(') {
            $index++; // Skip opening parenthesis
            $result = $this->parse_expression($tokens, $index);

            if ($index < count($tokens) && $tokens[$index] === ')') {
                $index++; // Skip closing parenthesis
            }

            return $result;
        }

        if ($token === '-') {
            $index++; // Skip minus sign
            return -$this->parse_factor($tokens, $index);
        }

        if ($token === '+') {
            $index++; // Skip plus sign
            return $this->parse_factor($tokens, $index);
        }

        // Unknown token
        $index++;
        return 0;
    }
    
    /**
     * Format value based on field display type
     */
    private function format_value($value, $field, $settings) {
        $display_type = isset($field['display_type']) ? $field['display_type'] : 'number';

        switch ($display_type) {
            case 'currency':
                return $this->format_currency($value, $settings);
            case 'percentage':
                return number_format($value, 2) . '%';
            case 'number':
            default:
                return number_format($value, 2);
        }
    }

    /**
     * Format currency value
     */
    private function format_currency($value, $settings) {
        $decimal_places = isset($settings['decimal_places']) ? intval($settings['decimal_places']) : 2;
        $decimal_separator = isset($settings['decimal_separator']) ? $settings['decimal_separator'] : '.';
        $thousand_separator = isset($settings['thousand_separator']) ? $settings['thousand_separator'] : ',';
        $currency_symbol = isset($settings['currency_symbol']) ? $settings['currency_symbol'] : '$';
        $currency_position = isset($settings['currency_position']) ? $settings['currency_position'] : 'left';

        $formatted_number = number_format($value, $decimal_places, $decimal_separator, $thousand_separator);

        if ($currency_position === 'right') {
            return $formatted_number . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted_number;
        }
    }
}
