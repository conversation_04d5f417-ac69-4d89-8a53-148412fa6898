<?php
/**
 * CFB Calculator Variables Management
 * Handles global variables that can be used in formulas
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Variables {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_cfb_save_variable', array($this, 'ajax_save_variable'));
        add_action('wp_ajax_cfb_delete_variable', array($this, 'ajax_delete_variable'));
        add_action('wp_ajax_cfb_get_variables', array($this, 'ajax_get_variables'));
    }
    
    /**
     * Render variables management page
     */
    public function render_variables_page() {
        $variables = CFB_Database::get_instance()->get_variables();
        $categories = CFB_Database::get_instance()->get_variable_categories();
        ?>
        <div class="wrap cfb-variables-page">
            <div class="cfb-variables-header">
                <h1>
                    <span class="dashicons dashicons-admin-settings"></span>
                    <?php _e('Global Variables', 'cfb-calculator'); ?>
                </h1>
                <button type="button" class="button-primary cfb-add-variable-btn">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php _e('Add New Variable', 'cfb-calculator'); ?>
                </button>
            </div>
            
            <div class="cfb-variables-description">
                <p><?php _e('Create global variables that can be used in your calculator formulas. Variables are available across all forms and can be referenced using {variable_name} syntax.', 'cfb-calculator'); ?></p>
            </div>
            
            <!-- Variables Grid -->
            <div class="cfb-variables-grid" id="cfb-variables-grid">
                <?php if (empty($variables)): ?>
                    <div class="cfb-no-variables">
                        <div class="cfb-no-variables-icon">
                            <span class="dashicons dashicons-admin-settings"></span>
                        </div>
                        <h3><?php _e('No Variables Yet', 'cfb-calculator'); ?></h3>
                        <p><?php _e('Create your first global variable to use in calculator formulas.', 'cfb-calculator'); ?></p>
                        <button type="button" class="button-primary cfb-add-variable-btn">
                            <span class="dashicons dashicons-plus-alt"></span>
                            <?php _e('Add Your First Variable', 'cfb-calculator'); ?>
                        </button>
                    </div>
                <?php else: ?>
                    <?php foreach ($variables as $variable): ?>
                        <div class="cfb-variable-card" data-variable-id="<?php echo $variable->id; ?>">
                            <div class="cfb-variable-header" style="border-left: 4px solid <?php echo esc_attr($variable->color); ?>">
                                <div class="cfb-variable-icon" style="color: <?php echo esc_attr($variable->color); ?>">
                                    <span class="dashicons <?php echo esc_attr($variable->icon); ?>"></span>
                                </div>
                                <div class="cfb-variable-info">
                                    <h3 class="cfb-variable-name"><?php echo esc_html($variable->label); ?></h3>
                                    <code class="cfb-variable-code">{<?php echo esc_html($variable->name); ?>}</code>
                                </div>
                                <div class="cfb-variable-actions">
                                    <button type="button" class="cfb-edit-variable" data-variable-id="<?php echo $variable->id; ?>" title="<?php _e('Edit Variable', 'cfb-calculator'); ?>">
                                        <span class="dashicons dashicons-edit"></span>
                                    </button>
                                    <button type="button" class="cfb-delete-variable" data-variable-id="<?php echo $variable->id; ?>" title="<?php _e('Delete Variable', 'cfb-calculator'); ?>">
                                        <span class="dashicons dashicons-trash"></span>
                                    </button>
                                </div>
                            </div>
                            <div class="cfb-variable-body">
                                <div class="cfb-variable-value">
                                    <span class="cfb-variable-value-label"><?php _e('Value:', 'cfb-calculator'); ?></span>
                                    <span class="cfb-variable-value-amount"><?php echo number_format($variable->value, 2); ?></span>
                                </div>
                                <?php if ($variable->description): ?>
                                    <div class="cfb-variable-description">
                                        <?php echo esc_html($variable->description); ?>
                                    </div>
                                <?php endif; ?>
                                <div class="cfb-variable-meta">
                                    <span class="cfb-variable-category"><?php echo esc_html(ucfirst($variable->category)); ?></span>
                                    <span class="cfb-variable-status <?php echo $variable->is_active ? 'active' : 'inactive'; ?>">
                                        <?php echo $variable->is_active ? __('Active', 'cfb-calculator') : __('Inactive', 'cfb-calculator'); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Variable Modal -->
        <div id="cfb-variable-modal" class="cfb-modal" style="display: none;">
            <div class="cfb-modal-content">
                <div class="cfb-modal-header">
                    <h2 id="cfb-modal-title"><?php _e('Add New Variable', 'cfb-calculator'); ?></h2>
                    <button type="button" class="cfb-modal-close">
                        <span class="dashicons dashicons-no"></span>
                    </button>
                </div>
                <div class="cfb-modal-body">
                    <form id="cfb-variable-form">
                        <input type="hidden" id="variable-id" name="id" value="">
                        
                        <div class="cfb-form-row">
                            <div class="cfb-form-group">
                                <label for="variable-name"><?php _e('Variable Name', 'cfb-calculator'); ?> *</label>
                                <input type="text" id="variable-name" name="name" required 
                                       placeholder="<?php _e('e.g., tax_rate, shipping_cost', 'cfb-calculator'); ?>"
                                       pattern="[a-z_][a-z0-9_]*">
                                <small><?php _e('Use lowercase letters, numbers, and underscores only. No spaces.', 'cfb-calculator'); ?></small>
                            </div>
                            <div class="cfb-form-group">
                                <label for="variable-label"><?php _e('Display Label', 'cfb-calculator'); ?> *</label>
                                <input type="text" id="variable-label" name="label" required 
                                       placeholder="<?php _e('e.g., Tax Rate, Shipping Cost', 'cfb-calculator'); ?>">
                            </div>
                        </div>
                        
                        <div class="cfb-form-row">
                            <div class="cfb-form-group">
                                <label for="variable-value"><?php _e('Value', 'cfb-calculator'); ?> *</label>
                                <input type="number" id="variable-value" name="value" step="0.0001" required 
                                       placeholder="<?php _e('e.g., 0.08, 15.50', 'cfb-calculator'); ?>">
                            </div>
                            <div class="cfb-form-group">
                                <label for="variable-category"><?php _e('Category', 'cfb-calculator'); ?></label>
                                <select id="variable-category" name="category">
                                    <option value="general"><?php _e('General', 'cfb-calculator'); ?></option>
                                    <option value="pricing"><?php _e('Pricing', 'cfb-calculator'); ?></option>
                                    <option value="taxes"><?php _e('Taxes', 'cfb-calculator'); ?></option>
                                    <option value="shipping"><?php _e('Shipping', 'cfb-calculator'); ?></option>
                                    <option value="discounts"><?php _e('Discounts', 'cfb-calculator'); ?></option>
                                    <option value="custom"><?php _e('Custom', 'cfb-calculator'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="cfb-form-row">
                            <div class="cfb-form-group">
                                <label for="variable-icon"><?php _e('Icon', 'cfb-calculator'); ?></label>
                                <select id="variable-icon" name="icon">
                                    <option value="dashicons-admin-settings"><?php _e('Settings', 'cfb-calculator'); ?></option>
                                    <option value="dashicons-money-alt"><?php _e('Money', 'cfb-calculator'); ?></option>
                                    <option value="dashicons-cart"><?php _e('Cart', 'cfb-calculator'); ?></option>
                                    <option value="dashicons-tag"><?php _e('Tag', 'cfb-calculator'); ?></option>
                                    <option value="dashicons-calculator"><?php _e('Calculator', 'cfb-calculator'); ?></option>
                                    <option value="dashicons-chart-line"><?php _e('Chart', 'cfb-calculator'); ?></option>
                                    <option value="dashicons-star-filled"><?php _e('Star', 'cfb-calculator'); ?></option>
                                    <option value="dashicons-location"><?php _e('Location', 'cfb-calculator'); ?></option>
                                </select>
                            </div>
                            <div class="cfb-form-group">
                                <label for="variable-color"><?php _e('Color', 'cfb-calculator'); ?></label>
                                <input type="color" id="variable-color" name="color" value="#667eea">
                            </div>
                        </div>
                        
                        <div class="cfb-form-group">
                            <label for="variable-description"><?php _e('Description', 'cfb-calculator'); ?></label>
                            <textarea id="variable-description" name="description" rows="3" 
                                      placeholder="<?php _e('Optional description of what this variable represents...', 'cfb-calculator'); ?>"></textarea>
                        </div>
                        
                        <div class="cfb-form-group">
                            <label class="cfb-checkbox-label">
                                <input type="checkbox" id="variable-active" name="is_active" checked>
                                <span><?php _e('Active (available in formulas)', 'cfb-calculator'); ?></span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="cfb-modal-footer">
                    <button type="button" class="button" id="cfb-cancel-variable"><?php _e('Cancel', 'cfb-calculator'); ?></button>
                    <button type="button" class="button-primary" id="cfb-save-variable"><?php _e('Save Variable', 'cfb-calculator'); ?></button>
                </div>
            </div>
        </div>
        
        <style>
        .cfb-variables-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .cfb-variables-header h1 {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0;
        }
        .cfb-variables-description {
            background: #f0f6fc;
            border: 1px solid #c8e1ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .cfb-variables-description p {
            margin: 0;
            color: #0969da;
        }
        .cfb-variables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        .cfb-variable-card {
            background: #fff;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.2s ease;
        }
        .cfb-variable-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .cfb-variable-header {
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            background: #f8f9fa;
        }
        .cfb-variable-icon {
            font-size: 20px;
        }
        .cfb-variable-info {
            flex: 1;
        }
        .cfb-variable-name {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
        }
        .cfb-variable-code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            color: #495057;
        }
        .cfb-variable-actions {
            display: flex;
            gap: 4px;
        }
        .cfb-variable-actions button {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 4px;
            cursor: pointer;
            color: #6c757d;
            transition: all 0.2s ease;
        }
        .cfb-variable-actions button:hover {
            background: #e9ecef;
            color: #495057;
        }
        .cfb-variable-body {
            padding: 16px;
        }
        .cfb-variable-value {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        .cfb-variable-value-label {
            font-weight: 600;
            color: #495057;
        }
        .cfb-variable-value-amount {
            font-size: 18px;
            font-weight: 700;
            color: #28a745;
        }
        .cfb-variable-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .cfb-variable-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }
        .cfb-variable-category {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 12px;
            color: #495057;
            font-weight: 500;
        }
        .cfb-variable-status.active {
            color: #28a745;
            font-weight: 600;
        }
        .cfb-variable-status.inactive {
            color: #dc3545;
            font-weight: 600;
        }
        .cfb-no-variables {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
        }
        .cfb-no-variables-icon {
            font-size: 48px;
            color: #dee2e6;
            margin-bottom: 20px;
        }
        .cfb-no-variables h3 {
            color: #495057;
            margin-bottom: 8px;
        }
        .cfb-no-variables p {
            color: #6c757d;
            margin-bottom: 20px;
        }

        /* Modal Styles */
        .cfb-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 100000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .cfb-modal-content {
            background: #fff;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .cfb-modal-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .cfb-modal-header h2 {
            margin: 0;
            font-size: 18px;
        }
        .cfb-modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6c757d;
            padding: 5px;
        }
        .cfb-modal-close:hover {
            color: #495057;
        }
        .cfb-modal-body {
            padding: 20px;
        }
        .cfb-modal-footer {
            padding: 20px;
            border-top: 1px solid #e1e5e9;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        .cfb-form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .cfb-form-group {
            flex: 1;
        }
        .cfb-form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        .cfb-form-group input,
        .cfb-form-group select,
        .cfb-form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .cfb-form-group small {
            color: #6c757d;
            font-size: 12px;
        }
        .cfb-checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
        </style>
        <?php
    }
    
    /**
     * AJAX handler for saving variables
     */
    public function ajax_save_variable() {
        try {
            // Log the request for debugging
            error_log('CFB Variable Save Request: ' . print_r($_POST, true));

            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'cfb_admin_nonce')) {
                error_log('CFB Variable Save: Nonce verification failed');
                wp_send_json_error(__('Security check failed', 'cfb-calculator'));
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                error_log('CFB Variable Save: User lacks permissions');
                wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
                return;
            }

            // Validate required fields
            if (empty($_POST['name']) || empty($_POST['label']) || !isset($_POST['value'])) {
                error_log('CFB Variable Save: Missing required fields');
                wp_send_json_error(__('Missing required fields: name, label, and value are required', 'cfb-calculator'));
                return;
            }

            // Validate variable name format
            $name = sanitize_text_field($_POST['name']);
            if (!preg_match('/^[a-z_][a-z0-9_]*$/', $name)) {
                error_log('CFB Variable Save: Invalid variable name format: ' . $name);
                wp_send_json_error(__('Variable name must contain only lowercase letters, numbers, and underscores', 'cfb-calculator'));
                return;
            }

            // Prepare variable data
            $variable_data = array(
                'id' => isset($_POST['id']) ? intval($_POST['id']) : 0,
                'name' => $name,
                'label' => sanitize_text_field($_POST['label']),
                'value' => floatval($_POST['value']),
                'description' => sanitize_textarea_field($_POST['description']),
                'category' => sanitize_text_field($_POST['category']),
                'icon' => sanitize_text_field($_POST['icon']),
                'color' => sanitize_hex_color($_POST['color']),
                'is_active' => isset($_POST['is_active']) && $_POST['is_active'] ? 1 : 0
            );

            error_log('CFB Variable Save: Processed data: ' . print_r($variable_data, true));

            // Check if database class exists
            if (!class_exists('CFB_Database')) {
                error_log('CFB Variable Save: CFB_Database class not found');
                wp_send_json_error(__('Database class not available', 'cfb-calculator'));
                return;
            }

            // Get database instance
            $db = CFB_Database::get_instance();
            if (!$db) {
                error_log('CFB Variable Save: Failed to get database instance');
                wp_send_json_error(__('Database connection failed', 'cfb-calculator'));
                return;
            }

            // Save variable
            $variable_id = $db->save_variable($variable_data);

            if ($variable_id) {
                error_log('CFB Variable Save: Success - Variable ID: ' . $variable_id);
                wp_send_json_success(array(
                    'message' => __('Variable saved successfully', 'cfb-calculator'),
                    'variable_id' => $variable_id
                ));
            } else {
                error_log('CFB Variable Save: Database save failed');
                wp_send_json_error(__('Failed to save variable to database', 'cfb-calculator'));
            }

        } catch (Exception $e) {
            error_log('CFB Variable Save Exception: ' . $e->getMessage());
            wp_send_json_error(__('Error occurred while saving variable: ', 'cfb-calculator') . $e->getMessage());
        }
    }
    
    /**
     * AJAX handler for deleting variables
     */
    public function ajax_delete_variable() {
        check_ajax_referer('cfb_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }
        
        $variable_id = intval($_POST['variable_id']);
        
        if (CFB_Database::get_instance()->delete_variable($variable_id)) {
            wp_send_json_success(__('Variable deleted successfully', 'cfb-calculator'));
        } else {
            wp_send_json_error(__('Failed to delete variable', 'cfb-calculator'));
        }
    }
    
    /**
     * AJAX handler for getting variables
     */
    public function ajax_get_variables() {
        check_ajax_referer('cfb_admin_nonce', 'nonce');
        
        $variables = CFB_Database::get_instance()->get_variables();
        
        wp_send_json_success($variables);
    }
    
    /**
     * Get variables for formula builder
     */
    public function get_variables_for_formulas() {
        $variables = CFB_Database::get_instance()->get_variables();
        $formula_variables = array();
        
        foreach ($variables as $variable) {
            $formula_variables[] = array(
                'name' => $variable->name,
                'label' => $variable->label,
                'value' => $variable->value,
                'category' => $variable->category,
                'icon' => $variable->icon,
                'color' => $variable->color
            );
        }
        
        return $formula_variables;
    }
}
