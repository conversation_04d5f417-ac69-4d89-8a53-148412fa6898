<?php
/**
 * Test Complex Formula
 * Test the exact formula that's failing
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>Test Complex Formula</h1>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "✅ Formula engine created<br>";
    
    // Set up variables
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $property->setValue($formula_engine, array('dropdown_4' => 6000));
    
    echo "<h2>Testing Complex Formula Step by Step:</h2>";
    
    // Test the exact formula from the logs
    $formula = '5000000*(1+0.2*max(0,ceil((6000+100-5000)/1000)))';
    echo "<p><strong>Formula:</strong> <code>$formula</code></p>";
    
    // Test each component
    $components = array(
        '6000+100-5000' => 'Should be 1100',
        '(6000+100-5000)/1000' => 'Should be 1.1',
        'ceil((6000+100-5000)/1000)' => 'Should be 2',
        'max(0,ceil((6000+100-5000)/1000))' => 'Should be 2',
        '0.2*max(0,ceil((6000+100-5000)/1000))' => 'Should be 0.4',
        '1+0.2*max(0,ceil((6000+100-5000)/1000))' => 'Should be 1.4',
        '5000000*(1+0.2*max(0,ceil((6000+100-5000)/1000)))' => 'Should be 7000000'
    );
    
    foreach ($components as $component => $expected) {
        echo "<h3>Testing: <code>$component</code></h3>";
        echo "<p>Expected: $expected</p>";
        
        try {
            $result = $formula_engine->evaluate_formula($component);
            echo "<p>Result: <strong style='color: green;'>$result</strong></p>";
            
            if ($result == 0) {
                echo "<p style='color: red;'>⚠️ Result is 0 - this might be the issue!</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
        echo "<hr>";
    }
    
    // Test with the original formula with variables
    echo "<h2>Testing Original Formula with Variables:</h2>";
    $original_formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))';
    echo "<p><strong>Original formula:</strong> <code>$original_formula</code></p>";
    
    try {
        $result = $formula_engine->evaluate_formula($original_formula);
        echo "<p>Result: <strong style='color: green; font-size: 20px;'>$result</strong></p>";
        
        if ($result == 0) {
            echo "<p style='color: red; font-size: 16px;'>❌ This is the problem! The formula is returning 0!</p>";
        } else {
            echo "<p style='color: green; font-size: 16px;'>✅ Formula is working correctly!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    
    // Test the mathematical expression validation
    echo "<h2>Testing Mathematical Expression Validation:</h2>";
    
    $test_expressions = array(
        '5000000*(1+0.2*2)',
        '5000000*1.4',
        '7000000'
    );
    
    foreach ($test_expressions as $expr) {
        echo "<h3>Testing: <code>$expr</code></h3>";
        
        // Test the safe_eval method directly
        $safe_eval_method = $reflection->getMethod('safe_eval');
        $safe_eval_method->setAccessible(true);
        
        try {
            $result = $safe_eval_method->invoke($formula_engine, $expr);
            echo "<p>safe_eval result: <strong>$result</strong></p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>safe_eval error: " . $e->getMessage() . "</p>";
        }
        
        // Test the parse_mathematical_expression method directly
        $parse_method = $reflection->getMethod('parse_mathematical_expression');
        $parse_method->setAccessible(true);
        
        try {
            $result = $parse_method->invoke($formula_engine, $expr);
            echo "<p>parse_mathematical_expression result: <strong>$result</strong></p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>parse_mathematical_expression error: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}

echo "<h2>Check Error Logs</h2>";
echo "<p>Check your error logs for detailed debugging information.</p>";
?>
