<?php
/**
 * Final Calculation Test
 * Comprehensive test to verify the "---" issue is fixed
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>🎯 Final Calculation Test</h1>";
echo "<p>Testing if the '---' display issue is now fixed...</p>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

global $wpdb;

echo "<h2>1. ✅ Verify Variables Setup</h2>";

$variables = $wpdb->get_results("SELECT name, value, is_active FROM {$wpdb->prefix}cfb_variables WHERE is_active = 1");
echo "<h3>Active Variables:</h3>";
foreach ($variables as $var) {
    echo "<p>• <strong>{$var->name}:</strong> {$var->value}</p>";
}

echo "<h2>2. 🧮 Test Formula Engine</h2>";

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    
    // Load variables
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    
    $var_array = array();
    foreach ($variables as $var) {
        $var_array[$var->name] = floatval($var->value);
    }
    $property->setValue($formula_engine, $var_array);
    
    // Test the formula
    $formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))';
    echo "<h3>Testing Formula:</h3>";
    echo "<p><code>$formula</code></p>";
    
    $result = $formula_engine->evaluate_formula($formula);
    echo "<div style='border: 2px solid #28a745; padding: 20px; background: #f8fff8; text-align: center;'>";
    echo "<h3 style='color: #28a745; margin: 0;'>Formula Result: " . number_format($result) . "</h3>";
    echo "</div>";
    
    if ($result == 0) {
        echo "<p style='color: red; font-size: 18px;'>❌ Still returning 0 - formula needs more debugging</p>";
    } else {
        echo "<p style='color: green; font-size: 18px;'>✅ Formula working correctly!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Formula error: " . $e->getMessage() . "</p>";
}

echo "<h2>3. 📡 Test AJAX Response</h2>";

// Test AJAX with Form ID 1
$_POST = array(
    'action' => 'cfb_calculate_price',
    'nonce' => wp_create_nonce('cfb_calculator_nonce'),
    'form_id' => 1,
    'form_data' => array(
        'dropdown_4' => '6000'
    )
);

try {
    ob_start();
    $formula_engine->calculate_price();
    $ajax_response = ob_get_clean();
    
    $response_data = json_decode($ajax_response, true);
    
    if ($response_data && $response_data['success']) {
        echo "<h3>✅ AJAX Success</h3>";
        echo "<p><strong>Calculations returned:</strong> " . count($response_data['data']['calculations']) . "</p>";
        echo "<p><strong>Total value:</strong> " . $response_data['data']['total'] . "</p>";
        echo "<p><strong>Formatted total:</strong> " . $response_data['data']['formatted_total'] . "</p>";
        
        if (!empty($response_data['data']['calculations'])) {
            echo "<h4>Calculation Details:</h4>";
            foreach ($response_data['data']['calculations'] as $calc) {
                $value = $calc['value'] ?? 0;
                $formatted = $calc['formatted'] ?? 'no format';
                $label = $calc['label'] ?? 'no label';
                
                echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
                echo "<strong>{$label}:</strong><br>";
                echo "Value: {$value}<br>";
                echo "Formatted: {$formatted}<br>";
                
                if ($value == 0) {
                    echo "<span style='color: red; font-weight: bold;'>⚠️ This will show as '---' in frontend</span>";
                } else {
                    echo "<span style='color: green; font-weight: bold;'>✅ This will show the formatted value</span>";
                }
                echo "</div>";
            }
        } else {
            echo "<p style='color: red;'>❌ No calculations returned - check form configuration</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ AJAX failed or returned error</p>";
        if ($response_data && isset($response_data['data'])) {
            echo "<p>Error: " . $response_data['data'] . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ AJAX error: " . $e->getMessage() . "</p>";
}

echo "<h2>4. 🔍 Frontend Display Logic Analysis</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 4px; border: 1px solid #ffeaa7;'>";
echo "<h3>Frontend JavaScript Logic:</h3>";
echo "<p>The frontend shows '---' when:</p>";
echo "<code>calculation.value === 0</code>";
echo "<br><br>";
echo "<p>Based on our test:</p>";

if (isset($response_data['data']['calculations']) && !empty($response_data['data']['calculations'])) {
    $first_calc = $response_data['data']['calculations'][0];
    $calc_value = $first_calc['value'] ?? 0;
    
    if ($calc_value == 0) {
        echo "<p style='color: red;'><strong>calculation.value = 0</strong> → Frontend will show <strong>'---'</strong></p>";
        echo "<p>🎯 <strong>This is why you see '---'</strong></p>";
    } else {
        echo "<p style='color: green;'><strong>calculation.value = $calc_value</strong> → Frontend will show <strong>formatted value</strong></p>";
        echo "<p>🎉 <strong>The '---' issue should be fixed!</strong></p>";
    }
} else {
    echo "<p style='color: red;'><strong>No calculations returned</strong> → Frontend will show <strong>'---'</strong></p>";
    echo "<p>🎯 <strong>This is why you see '---' - no calculations are being processed</strong></p>";
}
echo "</div>";

echo "<h2>5. 🎯 Issue Status & Next Steps</h2>";

if (isset($calc_value) && $calc_value > 0) {
    echo "<div style='border: 3px solid #28a745; padding: 20px; margin: 20px 0; background: #f8fff8;'>";
    echo "<h3 style='color: #28a745; margin: 0;'>🎉 ISSUE FIXED!</h3>";
    echo "<p>The calculation is now returning a proper value ($calc_value) instead of 0.</p>";
    echo "<p>Your frontend should now display the calculated result instead of '---'.</p>";
    echo "<p><strong>Test your actual form now!</strong></p>";
    echo "</div>";
} else {
    echo "<div style='border: 3px solid #dc3545; padding: 20px; margin: 20px 0; background: #f8d7da;'>";
    echo "<h3 style='color: #dc3545; margin: 0;'>❌ Issue Still Exists</h3>";
    echo "<p>The calculation is still returning 0 or no calculations are being processed.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Check that Form ID 1 has calculation fields properly configured</li>";
    echo "<li>Use the AI Settings page to debug the formula step by step</li>";
    echo "<li>Verify the form structure and field configuration</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<h2>6. 🔗 Quick Links</h2>";
echo "<ul>";
echo "<li><a href='" . admin_url('admin.php?page=cfb-calculator-ai-settings') . "' target='_blank'>AI Settings & Formula Builder</a></li>";
echo "<li><a href='" . admin_url('admin.php?page=cfb-calculator') . "' target='_blank'>CFB Calculator Forms</a></li>";
echo "<li><a href='" . admin_url('admin.php?page=cfb-calculator-settings') . "' target='_blank'>CFB Calculator Settings</a></li>";
echo "</ul>";

echo "<div style='border: 2px solid #007cba; padding: 20px; margin: 20px 0; background: #f0f8ff;'>";
echo "<h3 style='color: #007cba; margin: 0;'>🚀 Ready to Test!</h3>";
echo "<p>Go to your frontend form and click the calculation button.</p>";
echo "<p>You should now see the calculated result instead of '---'.</p>";
echo "</div>";
?>
