# CFB Calculator - Conditional Logic Demo

This document demonstrates how to set up and use conditional logic in the CFB Calculator plugin.

## How Conditional Logic Works

Conditional logic allows you to show or hide form fields based on the values of other fields. This creates dynamic forms that adapt to user input.

## Setting Up Conditional Logic

### Step 1: Create Your Base Fields

1. Go to **CFB Calculator > Add New Form**
2. Add your base fields first (the fields that will control visibility)
3. Example base fields:
   - Service Type (Dropdown): Basic, Premium, Enterprise
   - Quantity (Number): 1-100
   - Add-ons (Checkbox): Support, Training, Custom Setup

### Step 2: Add Conditional Fields

1. Add the fields you want to show/hide conditionally
2. Click the **Edit** button on the field
3. Scroll down to **Conditional Logic** section
4. Check **"Enable conditional logic"**

### Step 3: Configure Conditions

1. **Logic Type**: Choose "All conditions" or "Any condition"
   - **All conditions**: Field shows only when ALL conditions are met (AND logic)
   - **Any condition**: Field shows when ANY condition is met (OR logic)

2. **Add Conditions**: Click "Add Condition" to create rules
   - **Field**: Select which field to check
   - **Operator**: Choose comparison type
   - **Value**: Set the comparison value

## Available Operators

| Operator | Description | Example Use Case |
|----------|-------------|------------------|
| **equals** | Field value exactly matches | Service Type equals "Premium" |
| **not equals** | Field value doesn't match | Service Type not equals "Basic" |
| **greater than** | Numeric value is greater | Quantity greater than 10 |
| **less than** | Numeric value is less | Quantity less than 5 |
| **contains** | Text contains substring | Comments contains "urgent" |
| **is not empty** | Field has any value | Name is not empty |
| **is empty** | Field is empty | Optional field is empty |

## Example Scenarios

### Scenario 1: Premium Features
**Goal**: Show premium options only when "Premium" service is selected

**Setup**:
1. **Base Field**: Service Type (Dropdown)
   - Options: Basic ($100), Premium ($200), Enterprise ($500)

2. **Conditional Field**: Premium Features (Checkbox)
   - Condition: Service Type equals "Premium"
   - Options: Priority Support (+$50), Custom Branding (+$100)

### Scenario 2: Bulk Discount
**Goal**: Show bulk discount field when quantity is high

**Setup**:
1. **Base Field**: Quantity (Number)
   - Min: 1, Max: 1000

2. **Conditional Field**: Bulk Discount (Radio)
   - Condition: Quantity greater than 50
   - Options: 10% Discount, 15% Discount, 20% Discount

### Scenario 3: Multiple Conditions
**Goal**: Show enterprise features only for enterprise customers with high quantity

**Setup**:
1. **Base Fields**: 
   - Service Type (Dropdown): Basic, Premium, Enterprise
   - Quantity (Number): 1-1000

2. **Conditional Field**: Enterprise Add-ons (Checkbox)
   - Logic Type: All conditions
   - Condition 1: Service Type equals "Enterprise"
   - Condition 2: Quantity greater than 100

### Scenario 4: Complex Logic
**Goal**: Show special pricing for either premium customers OR high quantity basic customers

**Setup**:
1. **Conditional Field**: Special Pricing (Text)
   - Logic Type: Any condition
   - Condition 1: Service Type equals "Premium"
   - Condition 2: Service Type equals "Basic" AND Quantity greater than 200

## Best Practices

### 1. Field Order Matters
- Place controlling fields (dropdowns, radios) before conditional fields
- Users should see the controlling field first

### 2. Clear Field Names
- Use descriptive field names for easier condition setup
- Example: `service_type` instead of `field1`

### 3. Logical Flow
- Design forms that flow naturally
- Avoid circular dependencies (Field A depends on Field B, which depends on Field A)

### 4. Test Your Logic
- Always test all possible combinations
- Use the preview feature to verify behavior

### 5. User Experience
- Don't hide critical fields behind complex conditions
- Provide clear labels and descriptions

## Advanced Examples

### Dynamic Pricing Tiers
```
Base Field: Company Size (Dropdown)
- Small (1-10 employees)
- Medium (11-50 employees)  
- Large (51+ employees)

Conditional Fields:
1. Small Business Package (shows if Company Size = Small)
2. Enterprise Features (shows if Company Size = Large)
3. Volume Discount (shows if Company Size = Medium OR Large)
```

### Service Customization
```
Base Field: Service Type (Radio)
- Web Design
- Mobile App
- Both

Conditional Logic:
1. Web Features (shows if Service Type = Web Design OR Both)
2. Mobile Features (shows if Service Type = Mobile App OR Both)
3. Integration Options (shows if Service Type = Both)
```

### Geographic Pricing
```
Base Field: Location (Dropdown)
- North America
- Europe
- Asia
- Other

Conditional Fields:
1. Shipping Options (shows if Location ≠ Other)
2. Tax Information (shows if Location = North America OR Europe)
3. Currency Selection (shows if Location = Other)
```

## Troubleshooting

### Field Not Showing/Hiding
1. Check field names are correct
2. Verify operator selection
3. Test with simple conditions first
4. Check browser console for JavaScript errors

### Conditions Not Working
1. Ensure controlling field has a value
2. Check for typos in condition values
3. Verify logic type (All vs Any)
4. Test each condition individually

### Performance Issues
1. Limit complex nested conditions
2. Use efficient operators (equals vs contains)
3. Minimize number of conditional fields

## Formula Integration

Conditional fields work seamlessly with formulas:

```
Subtotal 1: {base_service} + {premium_features}
Subtotal 2: if({bulk_discount_enabled}, {subtotal_1} * 0.9, {subtotal_1})
Total: {subtotal_2} + {shipping}
```

The formula engine automatically handles conditional fields:
- Hidden fields return 0 in calculations
- Only visible field values are used
- Formulas update when fields show/hide

## Tips for Complex Forms

1. **Start Simple**: Begin with basic conditions, then add complexity
2. **Document Logic**: Keep notes on your conditional rules
3. **User Testing**: Have others test your forms
4. **Progressive Enhancement**: Build forms that work without JavaScript
5. **Mobile Friendly**: Test conditional logic on mobile devices

This conditional logic system makes your forms intelligent and user-friendly, showing only relevant options based on user selections.
