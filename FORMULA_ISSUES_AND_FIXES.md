# CFB Calculator - Formula Issues and Fixes

## Problem Analysis

Your formula `5000000*(1+0.2*max(0,ceil(({dropdown_4}+100- 5000) / 1000)))` was not working due to several critical issues in the formula engine.

## Issues Identified

### 1. **Parse Expression Reference Parameter Issue** ✅ FIXED
**Problem:** Fatal error when calling `parse_expression()` with literal value instead of reference
**Location:** Line 538 in `parse_mathematical_expression()`
**Fix:** Changed from `$this->parse_expression($tokens, 0)` to:
```php
$index = 0;
$result = $this->parse_expression($tokens, $index);
```

### 2. **Variable Recognition Issue** ⚠️ PARTIALLY FIXED
**Problem:** Variables like `{dropdown_4}` not being recognized or loaded
**Root Causes:**
- Variables table might not exist or be populated
- Global variables not being loaded into formula engine
- Field variables not being processed correctly

**Fixes Applied:**
- Enhanced variable loading with better error logging
- Added reflection-based testing for variable loading
- Improved variable replacement debugging

### 3. **Function Processing Issues** ⚠️ PARTIALLY FIXED
**Problem:** Nested functions like `max(0,ceil(...))` not processing correctly
**Root Causes:**
- Function replacement regex not handling nested functions properly
- Limited iterations for complex nested functions

**Fixes Applied:**
- Increased max iterations from 10 to 20
- Enhanced function replacement logging
- Improved nested function processing logic

### 4. **Mathematical Expression Validation** ⚠️ NEEDS MORE WORK
**Problem:** Regex validation too restrictive, rejecting valid expressions
**Current Issue:** The regex `/^[0-9+\-*\/\(\)\.]+$/` may still be too restrictive

**Potential Issues:**
- Decimal numbers like `10.5` might not tokenize correctly
- Negative numbers might not be handled properly
- Scientific notation not supported

## Current Status

### ✅ **Working:**
- Basic mathematical expressions: `2 + 3`, `10 * 5`
- Simple variable replacement: `{dropdown_4}`
- Basic function calls: `ceil(1.1)`, `max(0, 2)`

### ❌ **Still Problematic:**
- Complex nested functions: `max(0,ceil(({dropdown_4}+100-5000)/1000))`
- Variable loading from database
- Form field variable processing

## Recommended Next Steps

### 1. **Fix Variable Loading**
```php
// Ensure variables table exists and is populated
// Check CFB_Database::get_variables() returns data
// Verify load_global_variables() is called correctly
```

### 2. **Improve Function Processing**
```php
// Better regex for nested function detection
// Handle function parameters with spaces
// Support for more mathematical functions
```

### 3. **Enhanced Debugging**
```php
// Add more detailed logging at each step
// Create test cases for each component
// Validate tokenization process
```

## Test Cases to Verify

1. **Variable Loading Test:**
   ```php
   $formula_engine->evaluate_formula('{dropdown_4}'); // Should return field value
   ```

2. **Simple Function Test:**
   ```php
   $formula_engine->evaluate_formula('ceil(1.1)'); // Should return 2
   ```

3. **Nested Function Test:**
   ```php
   $formula_engine->evaluate_formula('max(0, ceil(1.1))'); // Should return 2
   ```

4. **Complex Formula Test:**
   ```php
   $formula_engine->evaluate_formula('5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))');
   ```

## Files Modified

1. **`includes/class-cfb-formula-engine.php`**
   - Fixed parse_expression reference parameter (Line 538-539)
   - Enhanced function replacement logic (Lines 402-438)
   - Improved mathematical expression validation (Lines 530-575)
   - Added detailed logging to evaluate_formula (Lines 374-394)

2. **Test Files Created:**
   - `test-calculation-fix.php` - Comprehensive debugging
   - `test-specific-formula.php` - Your specific formula testing
   - `test-simple-formula.php` - Step-by-step component testing

## Expected Results for Your Formula

For `dropdown_4 = 6000`:
1. `{dropdown_4}+100-5000` = `6000+100-5000` = `1100`
2. `1100/1000` = `1.1`
3. `ceil(1.1)` = `2`
4. `max(0,2)` = `2`
5. `0.2*2` = `0.4`
6. `1+0.4` = `1.4`
7. `5000000*1.4` = `7000000`

## Immediate Action Required

1. **Test the current fixes** using the test files
2. **Check error logs** for detailed debugging information
3. **Verify variable loading** is working correctly
4. **Test each component** of your formula individually

The main parse_expression error should now be fixed, but variable recognition and complex function processing may still need attention.
