# CFB Price Calculator

A beautiful and powerful WordPress plugin for creating price calculation forms with conditional logic, complex formulas, and multi-language support.

## Features

### 🎨 Beautiful UI
- Modern, responsive design
- Smooth animations and transitions
- RTL/LTR language support
- Multiple themes (Modern, Classic, Minimal)
- Mobile-friendly interface

### 📝 Form Fields
- **Text Field** - With calculation rules (per character, per word, fixed rate)
- **Number Field** - Min/max values, step controls
- **Slider** - Interactive range selector with value display
- **Dropdown** - Single selection with pricing options
- **Radio Buttons** - Single choice with visual pricing
- **Checkboxes** - Multiple selections with individual pricing

### 🧮 Advanced Calculations
- **Complex Formulas** - Support for mathematical operations
- **Built-in Functions**:
  - `ceil()` - Round up to nearest integer
  - `floor()` - Round down to nearest integer
  - `min()` - Minimum value
  - `max()` - Maximum value
  - `pow()` - Power/exponent
  - `if()` - Conditional logic
  - `ifelse()` - If-else conditions
- **Subtotals** - Multiple calculation lines
- **Total Formula** - Custom total calculation logic

### 🔄 Conditional Logic
- Show/hide fields based on other field values
- Multiple condition operators:
  - Equals / Not equals
  - Greater than / Less than
  - Contains / Not contains
  - Empty / Not empty
- AND/OR logic combinations

### 🌍 Multi-Language Support
- **RTL Support** - Perfect for Arabic, Hebrew, Farsi
- **LTR Support** - English and other left-to-right languages
- **Translation Ready** - .pot file included
- **Farsi Translation** - Complete Persian translation included

### 💰 Currency Features
- Flexible currency symbol positioning (left/right)
- Customizable decimal places (0-4)
- Thousand separators (comma, space, etc.)
- Decimal separators (dot, comma)
- RTL-aware currency display

### ⚙️ Settings & Configuration
- **Currency Settings** - Symbol, position, formatting
- **Display Settings** - Themes, animations, auto-calculate
- **Language Settings** - RTL support, default language
- **Advanced Settings** - Caching, debug mode, custom CSS

## Installation

1. Upload the plugin files to `/wp-content/plugins/cfb-calculator/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to 'CFB Calculator' in your admin menu to create forms

## Usage

### Creating a Form

1. Navigate to **CFB Calculator > Add New Form**
2. Enter form name and description
3. Drag field types from the palette to the canvas
4. Configure each field's settings and pricing
5. Set up conditional logic if needed
6. Configure subtotals and total formula
7. Save the form

### Adding to Pages/Posts

Use the shortcode: `[cfb_calculator id="FORM_ID"]`

Or use the form ID from the forms list page.

### Field Configuration

#### Text Field
```
- Label: Field display name
- Placeholder: Helper text
- Required: Make field mandatory
- Calculation Rule:
  - Per Character: Rate × character count
  - Per Word: Rate × word count
  - Fixed Rate: Fixed amount if not empty
```

#### Number/Slider Field
```
- Min/Max Values: Range limits
- Step: Increment value
- Default Value: Initial value
- Show Value: Display current value (slider only)
```

#### Dropdown/Radio/Checkbox
```
- Options: Label, value, and price for each option
- Placeholder: Default text (dropdown only)
- Required: Force selection
```

### Formula Examples

#### Basic Operations
```
{field_name} * 2
{quantity} * {price}
{length} * {width} * {height}
```

#### Using Functions
```
ceil({total} * 1.1)
max({option1}, {option2}, {option3})
if({premium_service}, {base_price} * 1.5, {base_price})
```

#### Subtotals
```
Subtotal 1: {material_cost} + {labor_cost}
Subtotal 2: {shipping} + {handling}
Total: subtotal_1 + subtotal_2
```

### Conditional Logic Examples

#### Show field if another field equals value
```
Field: premium_options
Condition: service_type equals "premium"
```

#### Show field if number is greater than value
```
Field: bulk_discount
Condition: quantity greater_than 10
```

#### Multiple conditions (AND logic)
```
Field: special_offer
Conditions: 
- membership_type equals "gold" AND
- order_total greater_than 1000
```

## Styling

### Custom CSS
Add custom styles in **Settings > Advanced > Custom CSS**:

```css
/* Custom form styling */
.cfb-calculator-wrapper {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Custom button styling */
.cfb-calculate-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 25px;
}

/* RTL specific styles */
.cfb-calculator-wrapper[dir="rtl"] .cfb-total-value {
    font-family: 'Vazir', sans-serif;
}
```

### Theme Customization
Override default styles by targeting these classes:
- `.cfb-calculator-wrapper` - Main container
- `.cfb-field` - Individual field wrapper
- `.cfb-calculation-results` - Results section
- `.cfb-total-value` - Total amount display

## Hooks & Filters

### Actions
```php
// Before calculation
do_action('cfb_before_calculation', $form_id, $form_data);

// After calculation
do_action('cfb_after_calculation', $form_id, $result);

// Form submission
do_action('cfb_form_submitted', $form_id, $submission_data);
```

### Filters
```php
// Modify calculation result
$result = apply_filters('cfb_calculation_result', $result, $form_id);

// Modify currency formatting
$formatted = apply_filters('cfb_format_currency', $formatted, $value, $settings);

// Modify field output
$field_html = apply_filters('cfb_field_html', $field_html, $field_config);
```

## Database Tables

The plugin creates three tables:
- `wp_cfb_forms` - Form configurations
- `wp_cfb_form_fields` - Individual field settings
- `wp_cfb_form_submissions` - User submissions (if enabled)

## Requirements

- WordPress 5.0+
- PHP 7.4+
- MySQL 5.6+

## Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For support and feature requests, please visit our support forum or contact us directly.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Complete form builder with drag & drop
- All field types with conditional logic
- Complex formula engine
- Multi-language support (English, Farsi)
- RTL/LTR support
- Beautiful responsive UI
- Comprehensive settings panel
