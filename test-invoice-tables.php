<?php
/**
 * Test Invoice Tables
 * Check if invoice tables exist and create them if needed
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>CFB Invoice Tables Test</h1>";

// Check if plugin is active
if (!class_exists('CFB_Calculator')) {
    echo "<p style='color: red;'>CFB Calculator plugin is not active!</p>";
    exit;
}

global $wpdb;

// Check if invoice tables exist
$invoices_table = $wpdb->prefix . 'cfb_invoices';
$invoice_items_table = $wpdb->prefix . 'cfb_invoice_items';

echo "<h2>Checking Tables:</h2>";

$invoices_exists = $wpdb->get_var("SHOW TABLES LIKE '$invoices_table'");
$items_exists = $wpdb->get_var("SHOW TABLES LIKE '$invoice_items_table'");

echo "<p>Invoices table ($invoices_table): " . ($invoices_exists ? '<span style="color: green;">EXISTS</span>' : '<span style="color: red;">MISSING</span>') . "</p>";
echo "<p>Invoice items table ($invoice_items_table): " . ($items_exists ? '<span style="color: green;">EXISTS</span>' : '<span style="color: red;">MISSING</span>') . "</p>";

// If tables don't exist, create them
if (!$invoices_exists || !$items_exists) {
    echo "<h2>Creating Missing Tables:</h2>";
    
    // Force table creation
    CFB_Database::get_instance()->create_tables();
    
    // Check again
    $invoices_exists = $wpdb->get_var("SHOW TABLES LIKE '$invoices_table'");
    $items_exists = $wpdb->get_var("SHOW TABLES LIKE '$invoice_items_table'");
    
    echo "<p>After creation - Invoices table: " . ($invoices_exists ? '<span style="color: green;">EXISTS</span>' : '<span style="color: red;">STILL MISSING</span>') . "</p>";
    echo "<p>After creation - Invoice items table: " . ($items_exists ? '<span style="color: green;">EXISTS</span>' : '<span style="color: red;">STILL MISSING</span>') . "</p>";
}

// Test invoice creation
if ($invoices_exists) {
    echo "<h2>Testing Invoice Creation:</h2>";
    
    $test_data = array(
        'invoice_number' => 'TEST-' . time(),
        'form_id' => 1,
        'submission_id' => null,
        'customer_name' => 'Test Customer',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '************',
        'customer_address' => 'Test Address',
        'subtotal' => 100.00,
        'tax_amount' => 10.00,
        'total_amount' => 110.00,
        'currency' => 'USD',
        'status' => 'draft',
        'notes' => 'Test invoice'
    );
    
    $result = $wpdb->insert(
        $invoices_table,
        $test_data,
        array('%s', '%d', '%d', '%s', '%s', '%s', '%s', '%f', '%f', '%f', '%s', '%s', '%s')
    );
    
    if ($result) {
        $invoice_id = $wpdb->insert_id;
        echo "<p style='color: green;'>Test invoice created successfully with ID: $invoice_id</p>";
        
        // Clean up test data
        $wpdb->delete($invoices_table, array('id' => $invoice_id), array('%d'));
        echo "<p>Test invoice cleaned up.</p>";
    } else {
        echo "<p style='color: red;'>Failed to create test invoice: " . $wpdb->last_error . "</p>";
    }
}

// Test AJAX endpoint
echo "<h2>Testing AJAX Endpoint:</h2>";
echo "<p>AJAX URL: " . admin_url('admin-ajax.php') . "</p>";
echo "<p>Nonce: " . wp_create_nonce('cfb_calculator_nonce') . "</p>";

// Check if AJAX actions are registered
global $wp_filter;
$save_invoice_actions = isset($wp_filter['wp_ajax_cfb_save_invoice']) ? 'YES' : 'NO';
$save_invoice_nopriv_actions = isset($wp_filter['wp_ajax_nopriv_cfb_save_invoice']) ? 'YES' : 'NO';

echo "<p>wp_ajax_cfb_save_invoice registered: <strong>$save_invoice_actions</strong></p>";
echo "<p>wp_ajax_nopriv_cfb_save_invoice registered: <strong>$save_invoice_nopriv_actions</strong></p>";

// Test JavaScript AJAX call
?>
<h2>Test AJAX Call:</h2>
<button id="test-ajax">Test Invoice Creation AJAX</button>
<div id="ajax-result"></div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
jQuery(document).ready(function($) {
    $('#test-ajax').on('click', function() {
        const button = $(this);
        button.prop('disabled', true).text('Testing...');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'cfb_save_invoice',
                nonce: '<?php echo wp_create_nonce('cfb_calculator_nonce'); ?>',
                customer_name: 'Test Customer',
                customer_email: '<EMAIL>',
                customer_phone: '************',
                customer_address: 'Test Address',
                form_id: 1,
                subtotal: 100.00,
                tax_amount: 10.00,
                total_amount: 110.00
            },
            success: function(response) {
                $('#ajax-result').html('<p style="color: green;">Success: ' + JSON.stringify(response) + '</p>');
            },
            error: function(xhr, status, error) {
                $('#ajax-result').html('<p style="color: red;">Error: ' + xhr.responseText + '</p>');
            },
            complete: function() {
                button.prop('disabled', false).text('Test Invoice Creation AJAX');
            }
        });
    });
});
</script>

<?php
echo "<h2>WordPress Debug Info:</h2>";
echo "<p>WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'ON' : 'OFF') . "</p>";
echo "<p>WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'ON' : 'OFF') . "</p>";
echo "<p>WordPress Version: " . get_bloginfo('version') . "</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
?>
