<?php
/**
 * Final Complete Test
 * Comprehensive test to verify the entire variable and calculation system is working
 */

// Load WordPress
require_once('../../../wp-config.php');
require_once('../../../wp-load.php');

echo "<h1>🎯 Final Complete Test</h1>";
echo "<p>Testing the complete fix for the '---' display issue...</p>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

global $wpdb;

echo "<h2>1. ✅ System Status Check</h2>";

// Check if variables table exists
$variables_table = $wpdb->prefix . 'cfb_variables';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$variables_table'") == $variables_table;
echo "<p>Variables table exists: " . ($table_exists ? "✅ Yes" : "❌ No") . "</p>";

// Check if forms exist
$forms_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}cfb_forms");
echo "<p>Forms in database: $forms_count</p>";

// Check Form ID 1 specifically
$form = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}cfb_forms WHERE id = 1");
if ($form) {
    echo "<p>✅ Form ID 1 exists: {$form->name}</p>";
    
    $form_data = json_decode($form->form_data, true);
    if (isset($form_data['fields'])) {
        echo "<p>Form has " . count($form_data['fields']) . " fields</p>";
        
        // Check for required fields
        $required_fields = ['dropdown_4', 'paper', 'print'];
        $missing_fields = [];
        
        foreach ($required_fields as $required_field) {
            $found = false;
            foreach ($form_data['fields'] as $field) {
                if ($field['name'] === $required_field) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $missing_fields[] = $required_field;
            }
        }
        
        if (empty($missing_fields)) {
            echo "<p style='color: green;'>✅ All required fields present in form</p>";
        } else {
            echo "<p style='color: red;'>❌ Missing fields: " . implode(', ', $missing_fields) . "</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ Form ID 1 not found</p>";
}

echo "<h2>2. 🧮 Formula Engine Test</h2>";

try {
    $formula_engine = CFB_Formula_Engine::get_instance();
    echo "<p>✅ Formula engine created</p>";
    
    // Test with manual variables
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $property->setValue($formula_engine, array(
        'dropdown_4' => 6000,
        'paper' => 105000,
        'print' => 5000000
    ));
    
    // Test the formula
    $formula = '5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))';
    echo "<h3>Testing Formula:</h3>";
    echo "<p><code>$formula</code></p>";
    
    $result = $formula_engine->evaluate_formula($formula);
    echo "<div style='border: 2px solid #28a745; padding: 15px; background: #f8fff8; text-align: center;'>";
    echo "<h3 style='color: #28a745; margin: 0;'>Formula Result: " . number_format($result) . "</h3>";
    echo "</div>";
    
    if ($result == 0) {
        echo "<p style='color: red; font-size: 18px;'>❌ Formula still returning 0</p>";
    } else {
        echo "<p style='color: green; font-size: 18px;'>✅ Formula working correctly!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Formula engine error: " . $e->getMessage() . "</p>";
}

echo "<h2>3. 📡 AJAX Calculation Test</h2>";

// Test the complete AJAX flow
try {
    $_POST = array(
        'action' => 'cfb_calculate_price',
        'nonce' => wp_create_nonce('cfb_calculator_nonce'),
        'form_id' => 1,
        'form_data' => array(
            'dropdown_4' => '6000',
            'paper' => '105000',
            'print' => '5000000'
        )
    );
    
    echo "<h3>AJAX Request:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    // Capture the AJAX response
    ob_start();
    $formula_engine = CFB_Formula_Engine::get_instance();
    $formula_engine->calculate_price();
    $ajax_response = ob_get_clean();
    
    echo "<h3>AJAX Response:</h3>";
    echo "<pre>" . htmlspecialchars($ajax_response) . "</pre>";
    
    $response_data = json_decode($ajax_response, true);
    
    if ($response_data && $response_data['success']) {
        echo "<div style='border: 2px solid #28a745; padding: 15px; background: #f8fff8;'>";
        echo "<h3 style='color: #28a745; margin: 0;'>✅ AJAX Success!</h3>";
        
        if (isset($response_data['data']['calculations'])) {
            echo "<h4>Calculations:</h4>";
            if (empty($response_data['data']['calculations'])) {
                echo "<p style='color: red;'>❌ No calculations returned</p>";
            } else {
                foreach ($response_data['data']['calculations'] as $calc) {
                    $value = $calc['value'] ?? 0;
                    $formatted = $calc['formatted'] ?? 'no format';
                    $label = $calc['label'] ?? 'no label';
                    
                    echo "<p><strong>{$label}:</strong> " . number_format($value) . " (formatted: {$formatted})</p>";
                    
                    if ($value == 0) {
                        echo "<p style='color: red; font-weight: bold;'>⚠️ This will show as '---' in frontend</p>";
                    } else {
                        echo "<p style='color: green; font-weight: bold;'>✅ This will show the calculated value</p>";
                    }
                }
            }
        }
        
        $total = $response_data['data']['total'] ?? 0;
        echo "<h4>Total: " . number_format($total) . "</h4>";
        
        if ($total > 0) {
            echo "<p style='color: green; font-size: 18px;'><strong>🎉 The '---' issue is FIXED!</strong></p>";
        }
        echo "</div>";
        
    } else {
        echo "<div style='border: 2px solid #dc3545; padding: 15px; background: #f8d7da;'>";
        echo "<h3 style='color: #dc3545; margin: 0;'>❌ AJAX Failed</h3>";
        if ($response_data && isset($response_data['data'])) {
            echo "<p>Error: " . $response_data['data'] . "</p>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ AJAX error: " . $e->getMessage() . "</p>";
}

echo "<h2>4. 🎯 Frontend Display Analysis</h2>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 4px; border: 1px solid #b3d9ff;'>";
echo "<h3>Frontend JavaScript Logic:</h3>";
echo "<p>The frontend shows '---' when <code>calculation.value === 0</code></p>";

if (isset($response_data['data']['calculations']) && !empty($response_data['data']['calculations'])) {
    $first_calc = $response_data['data']['calculations'][0];
    $calc_value = $first_calc['value'] ?? 0;
    
    if ($calc_value == 0) {
        echo "<p style='color: red; font-size: 16px;'><strong>Result: calculation.value = 0 → Frontend shows '---'</strong></p>";
        echo "<p>🎯 <strong>The issue is still present</strong></p>";
    } else {
        echo "<p style='color: green; font-size: 16px;'><strong>Result: calculation.value = " . number_format($calc_value) . " → Frontend shows formatted value</strong></p>";
        echo "<p>🎉 <strong>The '---' issue is FIXED!</strong></p>";
    }
} else {
    echo "<p style='color: red; font-size: 16px;'><strong>Result: No calculations → Frontend shows '---'</strong></p>";
    echo "<p>🎯 <strong>The issue is still present - no calculations being processed</strong></p>";
}
echo "</div>";

echo "<h2>5. 🔗 Quick Links for Further Testing</h2>";
echo "<ul>";
echo "<li><a href='" . admin_url('admin.php?page=cfb-calculator-ai-settings') . "' target='_blank'>🤖 AI Settings & Formula Builder</a></li>";
echo "<li><a href='" . admin_url('admin.php?page=cfb-calculator') . "' target='_blank'>📋 CFB Calculator Forms</a></li>";
echo "<li><a href='" . home_url() . "' target='_blank'>🌐 Frontend (test your actual form)</a></li>";
echo "</ul>";

echo "<h2>6. 📋 Summary & Next Steps</h2>";

if (isset($calc_value) && $calc_value > 0) {
    echo "<div style='border: 3px solid #28a745; padding: 20px; margin: 20px 0; background: #f8fff8;'>";
    echo "<h3 style='color: #28a745; margin: 0;'>🎉 SUCCESS! Issue Completely Fixed</h3>";
    echo "<ul>";
    echo "<li>✅ Formula engine working correctly</li>";
    echo "<li>✅ AJAX returning proper calculation values</li>";
    echo "<li>✅ Frontend will show formatted numbers instead of '---'</li>";
    echo "<li>✅ All required form fields are present</li>";
    echo "</ul>";
    echo "<p><strong>Action:</strong> Test your frontend form - it should now work perfectly!</p>";
    echo "</div>";
} else {
    echo "<div style='border: 3px solid #dc3545; padding: 20px; margin: 20px 0; background: #f8d7da;'>";
    echo "<h3 style='color: #dc3545; margin: 0;'>❌ Issue Still Exists</h3>";
    echo "<p>The calculation is still returning 0 or no calculations are being processed.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Use the AI Settings page to debug the formula step by step</li>";
    echo "<li>Check that your form has the correct calculation fields</li>";
    echo "<li>Verify all required form fields exist with correct names</li>";
    echo "<li>Test with simpler formulas first</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<div style='border: 2px solid #007cba; padding: 20px; margin: 20px 0; background: #f0f8ff;'>";
echo "<h3 style='color: #007cba; margin: 0;'>🛠️ Tools Available</h3>";
echo "<p>You now have professional-grade tools for formula management:</p>";
echo "<ul>";
echo "<li><strong>Formula Builder:</strong> Visual formula creation with function buttons</li>";
echo "<li><strong>Formula Tester:</strong> Test formulas before deployment</li>";
echo "<li><strong>Variables Manager:</strong> Manage global variables</li>";
echo "<li><strong>Debugging Tools:</strong> Step-by-step formula debugging</li>";
echo "</ul>";
echo "</div>";
?>
